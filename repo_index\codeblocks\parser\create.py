from typing import Optional

from repo_index.codeblocks.parser.parser import Code<PERSON>ars<PERSON>
from repo_index.codeblocks.parser.python import PythonParser
from repo_index.codeblocks.parser.cpp import CppParser


def is_supported(language: str) -> bool:
    return language in ['python', 'java', 'typescript', 'javascript', 'cpp', 'c++']


def create_parser(language: str, **kwargs) -> Optional[CodeParser]:
    if language == 'python':
        return PythonParser(**kwargs)
    elif language in ['cpp', 'c++']:
        return CppParser(**kwargs)

    raise NotImplementedError(f'Language {language} is not supported.')
