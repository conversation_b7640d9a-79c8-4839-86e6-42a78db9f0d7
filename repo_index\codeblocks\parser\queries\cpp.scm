(translation_unit . (_) @child.first @definition.module) @root

; Class definitions
(class_specifier
  (type_identifier) @identifier
  (field_declaration_list
    ("{") @child.first
  )
) @root @definition.class

; Struct definitions (similar to classes in C++)
(struct_specifier
  (type_identifier) @identifier
  (field_declaration_list
    ("{") @child.first
  )
) @root @definition.class

; Namespace definitions
(namespace_definition
  (namespace_identifier) @identifier
  (declaration_list
    ("{") @child.first
  )
) @root @definition.namespace

; Function definitions
(function_definition
  [
    (primitive_type) @return_type
    (type_identifier) @return_type
  ]
  (function_declarator
    (identifier) @identifier
    (parameter_list
      ("(")
      (
        (parameter_declaration
          [
            (primitive_type) @parameter.type
            (type_identifier) @parameter.type
          ]
          (identifier) @parameter.identifier
        )
        (",")?
      )*
      (")")
    )
  )
  (compound_statement
    ("{") @child.first
  )
) @root @definition.function

; Constructor definitions (inside classes)
(function_definition
  (function_declarator
    (identifier) @identifier
    (parameter_list
      ("(")
      (
        (parameter_declaration
          [
            (primitive_type) @parameter.type
            (type_identifier) @parameter.type
          ]
          (identifier) @parameter.identifier
        )
        (",")?
      )*
      (")")
    )
  )
  [
    (field_initializer_list) @child.first
    (compound_statement
      ("{") @child.first
    )
  ]
) @root @definition.constructor

; Method declarations (without body)
(declaration
  [
    (primitive_type) @return_type
    (type_identifier) @return_type
  ]
  (function_declarator
    (identifier) @identifier
    (parameter_list
      ("(")
      (
        (parameter_declaration
          [
            (primitive_type) @parameter.type
            (type_identifier) @parameter.type
          ]
          (identifier) @parameter.identifier
        )
        (",")?
      )*
      (")")
    )
  )
) @root @definition.function

; Include statements
(preproc_include
  [
    (system_lib_string) @reference.identifier @identifier
    (string_literal) @reference.identifier @identifier
  ]
) @root @definition.import

; Comments
(comment) @root @definition.comment

; Field declarations
(field_declaration
  [
    (primitive_type) @type
    (type_identifier) @type
  ]
  (field_identifier) @identifier
) @root @definition.field

; Variable declarations
(declaration
  [
    (primitive_type) @type
    (type_identifier) @type
  ]
  (identifier) @identifier
) @root @definition.variable

; Generic compound statements
(_
  (compound_statement
    . ("{") @child.first
  )
) @root @definition.statement

; If statements
(if_statement
  (compound_statement
    ("{") @child.first
  )
) @root @definition.compound

; For statements
(for_statement
  (compound_statement
    ("{") @child.first
  )
) @root @definition.compound

; While statements
(while_statement
  (compound_statement
    ("{") @child.first
  )
) @root @definition.compound

; Switch statements
(switch_statement
  (compound_statement
    ("{") @child.first
  )
) @root @definition.compound

; Try statements
(try_statement
  (compound_statement
    ("{") @child.first
  )
) @root @definition.compound

; Catch clauses
(catch_clause
  (compound_statement
    ("{") @child.first
  )
) @root @definition.dependent_clause
