from typing import Optional

from repo_index.codeblocks.parser.java import Java<PERSON>arser
from repo_index.codeblocks.parser.parser import CodeParser
from repo_index.codeblocks.parser.python import PythonParser
from repo_index.codeblocks.parser.cpp import <PERSON><PERSON><PERSON>ars<PERSON>


def supports_codeblocks(path: str):
    return (path.endswith('.py') or
            path.endswith('.cpp') or
            path.endswith('.cc') or
            path.endswith('.cxx') or
            path.endswith('.c++') or
            path.endswith('.hpp') or
            path.endswith('.h'))


def get_parser_by_path(file_path: str) -> Optional[CodeParser]:
    if file_path.endswith('.py'):
        return PythonParser()
    elif file_path.endswith('.java'):
        return JavaParser()
    elif (file_path.endswith('.cpp') or
          file_path.endswith('.cc') or
          file_path.endswith('.cxx') or
          file_path.endswith('.c++') or
          file_path.endswith('.hpp') or
          file_path.endswith('.h')):
        return CppParser()
    else:
        return None
