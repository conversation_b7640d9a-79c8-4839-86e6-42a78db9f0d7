{"summary": {"total_files": 327, "files_by_language": {"cpp": 325, "python": 2}, "total_dependencies": 2466, "files_with_dependencies": 169, "core_files": [{"file": "x86\\x86_usability.h", "dependents": 47}, {"file": "x86\\x86_activation.h", "dependents": 20}, {"file": "x86\\rvv\\elu_riscv.cpp", "dependents": 6}, {"file": "x86\\rvv\\elu_riscv.h", "dependents": 6}, {"file": "x86\\convolutiondepthwise_x86.cpp", "dependents": 5}], "cycles_detected": 106}, "files": [{"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\avx512_mathfun.h", "relative_path": "x86\\avx512_mathfun.h", "name": "avx512_mathfun.h", "extension": ".h", "language": "cpp", "size": 34589}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\avx_mathfun.h", "relative_path": "x86\\avx_mathfun.h", "name": "avx_mathfun.h", "extension": ".h", "language": "cpp", "size": 43215}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\batchnorm_x86.cpp", "relative_path": "x86\\batchnorm_x86.cpp", "name": "batchnorm_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 7433}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\batchnorm_x86.h", "relative_path": "x86\\batchnorm_x86.h", "name": "batchnorm_x86.h", "extension": ".h", "language": "cpp", "size": 1022}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\bias_x86.cpp", "relative_path": "x86\\bias_x86.cpp", "name": "bias_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 3580}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\bias_x86.h", "relative_path": "x86\\bias_x86.h", "name": "bias_x86.h", "extension": ".h", "language": "cpp", "size": 1103}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\binaryop_x86.cpp", "relative_path": "x86\\binaryop_x86.cpp", "name": "binaryop_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 32718}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\binaryop_x86.h", "relative_path": "x86\\binaryop_x86.h", "name": "binaryop_x86.h", "extension": ".h", "language": "cpp", "size": 1133}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\bnll_x86.cpp", "relative_path": "x86\\bnll_x86.cpp", "name": "bnll_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 3681}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\bnll_x86.h", "relative_path": "x86\\bnll_x86.h", "name": "bnll_x86.h", "extension": ".h", "language": "cpp", "size": 996}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\cast_bf16.h", "relative_path": "x86\\cast_bf16.h", "name": "cast_bf16.h", "extension": ".h", "language": "cpp", "size": 5144}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\cast_fp16.h", "relative_path": "x86\\cast_fp16.h", "name": "cast_fp16.h", "extension": ".h", "language": "cpp", "size": 4543}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\cast_x86.cpp", "relative_path": "x86\\cast_x86.cpp", "name": "cast_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 3365}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\cast_x86.h", "relative_path": "x86\\cast_x86.h", "name": "cast_x86.h", "extension": ".h", "language": "cpp", "size": 996}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\cast_x86_avx2.cpp", "relative_path": "x86\\cast_x86_avx2.cpp", "name": "cast_x86_avx2.cpp", "extension": ".cpp", "language": "cpp", "size": 1135}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\cast_x86_avx512bf16.cpp", "relative_path": "x86\\cast_x86_avx512bf16.cpp", "name": "cast_x86_avx512bf16.cpp", "extension": ".cpp", "language": "cpp", "size": 1147}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\cast_x86_f16c.cpp", "relative_path": "x86\\cast_x86_f16c.cpp", "name": "cast_x86_f16c.cpp", "extension": ".cpp", "language": "cpp", "size": 1135}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\clip_x86.cpp", "relative_path": "x86\\clip_x86.cpp", "name": "clip_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 3149}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\clip_x86.h", "relative_path": "x86\\clip_x86.h", "name": "clip_x86.h", "extension": ".h", "language": "cpp", "size": 987}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\concat_x86.cpp", "relative_path": "x86\\concat_x86.cpp", "name": "concat_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 28727}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\concat_x86.h", "relative_path": "x86\\concat_x86.h", "name": "concat_x86.h", "extension": ".h", "language": "cpp", "size": 1038}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution1d_packed.h", "relative_path": "x86\\convolution1d_packed.h", "name": "convolution1d_packed.h", "extension": ".h", "language": "cpp", "size": 120577}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution1d_x86.cpp", "relative_path": "x86\\convolution1d_x86.cpp", "name": "convolution1d_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 4735}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution1d_x86.h", "relative_path": "x86\\convolution1d_x86.h", "name": "convolution1d_x86.h", "extension": ".h", "language": "cpp", "size": 1316}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolutiondepthwise_3x3.h", "relative_path": "x86\\convolutiondepthwise_3x3.h", "name": "convolutiondepthwise_3x3.h", "extension": ".h", "language": "cpp", "size": 5364}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolutiondepthwise_3x3_int8.h", "relative_path": "x86\\convolutiondepthwise_3x3_int8.h", "name": "convolutiondepthwise_3x3_int8.h", "extension": ".h", "language": "cpp", "size": 11924}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolutiondepthwise_3x3_pack16.h", "relative_path": "x86\\convolutiondepthwise_3x3_pack16.h", "name": "convolutiondepthwise_3x3_pack16.h", "extension": ".h", "language": "cpp", "size": 31330}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolutiondepthwise_3x3_pack4.h", "relative_path": "x86\\convolutiondepthwise_3x3_pack4.h", "name": "convolutiondepthwise_3x3_pack4.h", "extension": ".h", "language": "cpp", "size": 25020}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolutiondepthwise_3x3_pack8.h", "relative_path": "x86\\convolutiondepthwise_3x3_pack8.h", "name": "convolutiondepthwise_3x3_pack8.h", "extension": ".h", "language": "cpp", "size": 35036}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolutiondepthwise_5x5_pack16.h", "relative_path": "x86\\convolutiondepthwise_5x5_pack16.h", "name": "convolutiondepthwise_5x5_pack16.h", "extension": ".h", "language": "cpp", "size": 12233}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolutiondepthwise_5x5_pack4.h", "relative_path": "x86\\convolutiondepthwise_5x5_pack4.h", "name": "convolutiondepthwise_5x5_pack4.h", "extension": ".h", "language": "cpp", "size": 19219}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolutiondepthwise_5x5_pack8.h", "relative_path": "x86\\convolutiondepthwise_5x5_pack8.h", "name": "convolutiondepthwise_5x5_pack8.h", "extension": ".h", "language": "cpp", "size": 12440}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolutiondepthwise_x86.cpp", "relative_path": "x86\\convolutiondepthwise_x86.cpp", "name": "convolutiondepthwise_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 44488}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolutiondepthwise_x86.h", "relative_path": "x86\\convolutiondepthwise_x86.h", "name": "convolutiondepthwise_x86.h", "extension": ".h", "language": "cpp", "size": 1651}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_1x1.h", "relative_path": "x86\\convolution_1x1.h", "name": "convolution_1x1.h", "extension": ".h", "language": "cpp", "size": 5606}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_2x2_pack8.h", "relative_path": "x86\\convolution_2x2_pack8.h", "name": "convolution_2x2_pack8.h", "extension": ".h", "language": "cpp", "size": 18325}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_3x3.h", "relative_path": "x86\\convolution_3x3.h", "name": "convolution_3x3.h", "extension": ".h", "language": "cpp", "size": 6023}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_3x3_int8.h", "relative_path": "x86\\convolution_3x3_int8.h", "name": "convolution_3x3_int8.h", "extension": ".h", "language": "cpp", "size": 4351}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_3x3_pack16to1.h", "relative_path": "x86\\convolution_3x3_pack16to1.h", "name": "convolution_3x3_pack16to1.h", "extension": ".h", "language": "cpp", "size": 3699}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_3x3_pack1to4.h", "relative_path": "x86\\convolution_3x3_pack1to4.h", "name": "convolution_3x3_pack1to4.h", "extension": ".h", "language": "cpp", "size": 61464}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_3x3_pack1to8.h", "relative_path": "x86\\convolution_3x3_pack1to8.h", "name": "convolution_3x3_pack1to8.h", "extension": ".h", "language": "cpp", "size": 80603}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_3x3_pack8.h", "relative_path": "x86\\convolution_3x3_pack8.h", "name": "convolution_3x3_pack8.h", "extension": ".h", "language": "cpp", "size": 38631}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_3x3_pack8to1.h", "relative_path": "x86\\convolution_3x3_pack8to1.h", "name": "convolution_3x3_pack8to1.h", "extension": ".h", "language": "cpp", "size": 3672}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_3x3_winograd.h", "relative_path": "x86\\convolution_3x3_winograd.h", "name": "convolution_3x3_winograd.h", "extension": ".h", "language": "cpp", "size": 298160}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_3x3_winograd_int8.h", "relative_path": "x86\\convolution_3x3_winograd_int8.h", "name": "convolution_3x3_winograd_int8.h", "extension": ".h", "language": "cpp", "size": 276232}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_5x5.h", "relative_path": "x86\\convolution_5x5.h", "name": "convolution_5x5.h", "extension": ".h", "language": "cpp", "size": 6543}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_im2col_gemm.h", "relative_path": "x86\\convolution_im2col_gemm.h", "name": "convolution_im2col_gemm.h", "extension": ".h", "language": "cpp", "size": 179594}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_im2col_gemm_int8.h", "relative_path": "x86\\convolution_im2col_gemm_int8.h", "name": "convolution_im2col_gemm_int8.h", "extension": ".h", "language": "cpp", "size": 209672}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_packed.h", "relative_path": "x86\\convolution_packed.h", "name": "convolution_packed.h", "extension": ".h", "language": "cpp", "size": 128646}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_packed_int8.h", "relative_path": "x86\\convolution_packed_int8.h", "name": "convolution_packed_int8.h", "extension": ".h", "language": "cpp", "size": 260319}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_x86.cpp", "relative_path": "x86\\convolution_x86.cpp", "name": "convolution_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 43649}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_x86.h", "relative_path": "x86\\convolution_x86.h", "name": "convolution_x86.h", "extension": ".h", "language": "cpp", "size": 1833}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_x86_avx2.cpp", "relative_path": "x86\\convolution_x86_avx2.cpp", "name": "convolution_x86_avx2.cpp", "extension": ".cpp", "language": "cpp", "size": 2877}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_x86_avx512vnni.cpp", "relative_path": "x86\\convolution_x86_avx512vnni.cpp", "name": "convolution_x86_avx512vnni.cpp", "extension": ".cpp", "language": "cpp", "size": 2077}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_x86_avxvnni.cpp", "relative_path": "x86\\convolution_x86_avxvnni.cpp", "name": "convolution_x86_avxvnni.cpp", "extension": ".cpp", "language": "cpp", "size": 2065}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_x86_avxvnniint8.cpp", "relative_path": "x86\\convolution_x86_avxvnniint8.cpp", "name": "convolution_x86_avxvnniint8.cpp", "extension": ".cpp", "language": "cpp", "size": 1219}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\convolution_x86_xop.cpp", "relative_path": "x86\\convolution_x86_xop.cpp", "name": "convolution_x86_xop.cpp", "extension": ".cpp", "language": "cpp", "size": 2067}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\crop_x86.cpp", "relative_path": "x86\\crop_x86.cpp", "name": "crop_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 32644}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\crop_x86.h", "relative_path": "x86\\crop_x86.h", "name": "crop_x86.h", "extension": ".h", "language": "cpp", "size": 1114}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolutiondepthwise_x86.cpp", "relative_path": "x86\\deconvolutiondepthwise_x86.cpp", "name": "deconvolutiondepthwise_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 22922}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolutiondepthwise_x86.h", "relative_path": "x86\\deconvolutiondepthwise_x86.h", "name": "deconvolutiondepthwise_x86.h", "extension": ".h", "language": "cpp", "size": 1478}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack16.h", "relative_path": "x86\\deconvolution_pack16.h", "name": "deconvolution_pack16.h", "extension": ".h", "language": "cpp", "size": 6842}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack16to1.h", "relative_path": "x86\\deconvolution_pack16to1.h", "name": "deconvolution_pack16to1.h", "extension": ".h", "language": "cpp", "size": 3658}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack16to4.h", "relative_path": "x86\\deconvolution_pack16to4.h", "name": "deconvolution_pack16to4.h", "extension": ".h", "language": "cpp", "size": 6713}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack16to8.h", "relative_path": "x86\\deconvolution_pack16to8.h", "name": "deconvolution_pack16to8.h", "extension": ".h", "language": "cpp", "size": 6917}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack1to16.h", "relative_path": "x86\\deconvolution_pack1to16.h", "name": "deconvolution_pack1to16.h", "extension": ".h", "language": "cpp", "size": 3652}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack1to4.h", "relative_path": "x86\\deconvolution_pack1to4.h", "name": "deconvolution_pack1to4.h", "extension": ".h", "language": "cpp", "size": 3628}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack1to8.h", "relative_path": "x86\\deconvolution_pack1to8.h", "name": "deconvolution_pack1to8.h", "extension": ".h", "language": "cpp", "size": 3646}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack4.h", "relative_path": "x86\\deconvolution_pack4.h", "name": "deconvolution_pack4.h", "extension": ".h", "language": "cpp", "size": 4219}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack4to1.h", "relative_path": "x86\\deconvolution_pack4to1.h", "name": "deconvolution_pack4to1.h", "extension": ".h", "language": "cpp", "size": 3636}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack4to16.h", "relative_path": "x86\\deconvolution_pack4to16.h", "name": "deconvolution_pack4to16.h", "extension": ".h", "language": "cpp", "size": 4254}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack4to8.h", "relative_path": "x86\\deconvolution_pack4to8.h", "name": "deconvolution_pack4to8.h", "extension": ".h", "language": "cpp", "size": 4284}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack8.h", "relative_path": "x86\\deconvolution_pack8.h", "name": "deconvolution_pack8.h", "extension": ".h", "language": "cpp", "size": 5165}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack8to1.h", "relative_path": "x86\\deconvolution_pack8to1.h", "name": "deconvolution_pack8to1.h", "extension": ".h", "language": "cpp", "size": 3651}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack8to16.h", "relative_path": "x86\\deconvolution_pack8to16.h", "name": "deconvolution_pack8to16.h", "extension": ".h", "language": "cpp", "size": 5120}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_pack8to4.h", "relative_path": "x86\\deconvolution_pack8to4.h", "name": "deconvolution_pack8to4.h", "extension": ".h", "language": "cpp", "size": 5086}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_x86.cpp", "relative_path": "x86\\deconvolution_x86.cpp", "name": "deconvolution_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 25379}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deconvolution_x86.h", "relative_path": "x86\\deconvolution_x86.h", "name": "deconvolution_x86.h", "extension": ".h", "language": "cpp", "size": 1357}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack16.h", "relative_path": "x86\\deformableconv2d_pack16.h", "name": "deformableconv2d_pack16.h", "extension": ".h", "language": "cpp", "size": 31294}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack16to1.h", "relative_path": "x86\\deformableconv2d_pack16to1.h", "name": "deformableconv2d_pack16to1.h", "extension": ".h", "language": "cpp", "size": 23602}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack16to4.h", "relative_path": "x86\\deformableconv2d_pack16to4.h", "name": "deformableconv2d_pack16to4.h", "extension": ".h", "language": "cpp", "size": 30921}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack16to8.h", "relative_path": "x86\\deformableconv2d_pack16to8.h", "name": "deformableconv2d_pack16to8.h", "extension": ".h", "language": "cpp", "size": 31847}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack1to16.h", "relative_path": "x86\\deformableconv2d_pack1to16.h", "name": "deformableconv2d_pack1to16.h", "extension": ".h", "language": "cpp", "size": 10037}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack1to4.h", "relative_path": "x86\\deformableconv2d_pack1to4.h", "name": "deformableconv2d_pack1to4.h", "extension": ".h", "language": "cpp", "size": 9667}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack1to8.h", "relative_path": "x86\\deformableconv2d_pack1to8.h", "name": "deformableconv2d_pack1to8.h", "extension": ".h", "language": "cpp", "size": 9854}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack4.h", "relative_path": "x86\\deformableconv2d_pack4.h", "name": "deformableconv2d_pack4.h", "extension": ".h", "language": "cpp", "size": 13951}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack4to1.h", "relative_path": "x86\\deformableconv2d_pack4to1.h", "name": "deformableconv2d_pack4to1.h", "extension": ".h", "language": "cpp", "size": 11160}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack4to16.h", "relative_path": "x86\\deformableconv2d_pack4to16.h", "name": "deformableconv2d_pack4to16.h", "extension": ".h", "language": "cpp", "size": 14324}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack4to8.h", "relative_path": "x86\\deformableconv2d_pack4to8.h", "name": "deformableconv2d_pack4to8.h", "extension": ".h", "language": "cpp", "size": 14288}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack8.h", "relative_path": "x86\\deformableconv2d_pack8.h", "name": "deformableconv2d_pack8.h", "extension": ".h", "language": "cpp", "size": 20125}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack8to1.h", "relative_path": "x86\\deformableconv2d_pack8to1.h", "name": "deformableconv2d_pack8to1.h", "extension": ".h", "language": "cpp", "size": 15160}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack8to16.h", "relative_path": "x86\\deformableconv2d_pack8to16.h", "name": "deformableconv2d_pack8to16.h", "extension": ".h", "language": "cpp", "size": 19970}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_pack8to4.h", "relative_path": "x86\\deformableconv2d_pack8to4.h", "name": "deformableconv2d_pack8to4.h", "extension": ".h", "language": "cpp", "size": 19598}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_x86.cpp", "relative_path": "x86\\deformableconv2d_x86.cpp", "name": "deformableconv2d_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 34540}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\deformableconv2d_x86.h", "relative_path": "x86\\deformableconv2d_x86.h", "name": "deformableconv2d_x86.h", "extension": ".h", "language": "cpp", "size": 1289}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\dequantize_x86.cpp", "relative_path": "x86\\dequantize_x86.cpp", "name": "dequantize_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 7946}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\dequantize_x86.h", "relative_path": "x86\\dequantize_x86.h", "name": "dequantize_x86.h", "extension": ".h", "language": "cpp", "size": 1038}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\dropout_x86.cpp", "relative_path": "x86\\dropout_x86.cpp", "name": "dropout_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 4799}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\dropout_x86.h", "relative_path": "x86\\dropout_x86.h", "name": "dropout_x86.h", "extension": ".h", "language": "cpp", "size": 1008}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\eltwise_x86.cpp", "relative_path": "x86\\eltwise_x86.cpp", "name": "eltwise_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 17261}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\eltwise_x86.h", "relative_path": "x86\\eltwise_x86.h", "name": "eltwise_x86.h", "extension": ".h", "language": "cpp", "size": 1045}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\elu_x86.cpp", "relative_path": "x86\\elu_x86.cpp", "name": "elu_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 2321}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\elu_x86.h", "relative_path": "x86\\elu_x86.h", "name": "elu_x86.h", "extension": ".h", "language": "cpp", "size": 980}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\flatten_x86.cpp", "relative_path": "x86\\flatten_x86.cpp", "name": "flatten_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 26668}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\flatten_x86.h", "relative_path": "x86\\flatten_x86.h", "name": "flatten_x86.h", "extension": ".h", "language": "cpp", "size": 1115}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gelu_x86.cpp", "relative_path": "x86\\gelu_x86.cpp", "name": "gelu_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 4564}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gelu_x86.h", "relative_path": "x86\\gelu_x86.h", "name": "gelu_x86.h", "extension": ".h", "language": "cpp", "size": 1039}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gemm_int8.h", "relative_path": "x86\\gemm_int8.h", "name": "gemm_int8.h", "extension": ".h", "language": "cpp", "size": 640139}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gemm_x86.cpp", "relative_path": "x86\\gemm_x86.cpp", "name": "gemm_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 319657}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gemm_x86.h", "relative_path": "x86\\gemm_x86.h", "name": "gemm_x86.h", "extension": ".h", "language": "cpp", "size": 1719}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gemm_x86_avx2.cpp", "relative_path": "x86\\gemm_x86_avx2.cpp", "name": "gemm_x86_avx2.cpp", "extension": ".cpp", "language": "cpp", "size": 2996}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gemm_x86_avx512vnni.cpp", "relative_path": "x86\\gemm_x86_avx512vnni.cpp", "name": "gemm_x86_avx512vnni.cpp", "extension": ".cpp", "language": "cpp", "size": 2756}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gemm_x86_avxvnni.cpp", "relative_path": "x86\\gemm_x86_avxvnni.cpp", "name": "gemm_x86_avxvnni.cpp", "extension": ".cpp", "language": "cpp", "size": 2663}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gemm_x86_avxvnniint8.cpp", "relative_path": "x86\\gemm_x86_avxvnniint8.cpp", "name": "gemm_x86_avxvnniint8.cpp", "extension": ".cpp", "language": "cpp", "size": 2699}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gemm_x86_xop.cpp", "relative_path": "x86\\gemm_x86_xop.cpp", "name": "gemm_x86_xop.cpp", "extension": ".cpp", "language": "cpp", "size": 1245}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\generate_riscv_operators.py", "relative_path": "x86\\generate_riscv_operators.py", "name": "generate_riscv_operators.py", "extension": ".py", "language": "python", "size": 6312}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gridsample_bicubic_apply_interpolation.h", "relative_path": "x86\\gridsample_bicubic_apply_interpolation.h", "name": "gridsample_bicubic_apply_interpolation.h", "extension": ".h", "language": "cpp", "size": 13146}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gridsample_bicubic_compute_blob.h", "relative_path": "x86\\gridsample_bicubic_compute_blob.h", "name": "gridsample_bicubic_compute_blob.h", "extension": ".h", "language": "cpp", "size": 14383}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gridsample_bilinear_apply_interpolation.h", "relative_path": "x86\\gridsample_bilinear_apply_interpolation.h", "name": "gridsample_bilinear_apply_interpolation.h", "extension": ".h", "language": "cpp", "size": 16786}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gridsample_bilinear_compute_blob.h", "relative_path": "x86\\gridsample_bilinear_compute_blob.h", "name": "gridsample_bilinear_compute_blob.h", "extension": ".h", "language": "cpp", "size": 31570}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gridsample_compute_blob.h", "relative_path": "x86\\gridsample_compute_blob.h", "name": "gridsample_compute_blob.h", "extension": ".h", "language": "cpp", "size": 4113}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gridsample_nearest_apply_interpolation.h", "relative_path": "x86\\gridsample_nearest_apply_interpolation.h", "name": "gridsample_nearest_apply_interpolation.h", "extension": ".h", "language": "cpp", "size": 3966}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gridsample_nearest_compute_blob.h", "relative_path": "x86\\gridsample_nearest_compute_blob.h", "name": "gridsample_nearest_compute_blob.h", "extension": ".h", "language": "cpp", "size": 12374}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gridsample_x86.cpp", "relative_path": "x86\\gridsample_x86.cpp", "name": "gridsample_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 17244}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\gridsample_x86.h", "relative_path": "x86\\gridsample_x86.h", "name": "gridsample_x86.h", "extension": ".h", "language": "cpp", "size": 1066}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\groupnorm_x86.cpp", "relative_path": "x86\\groupnorm_x86.cpp", "name": "groupnorm_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 22925}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\groupnorm_x86.h", "relative_path": "x86\\groupnorm_x86.h", "name": "groupnorm_x86.h", "extension": ".h", "language": "cpp", "size": 1022}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\hardsigmoid_x86.cpp", "relative_path": "x86\\hardsigmoid_x86.cpp", "name": "hardsigmoid_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 3285}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\hardsigmoid_x86.h", "relative_path": "x86\\hardsigmoid_x86.h", "name": "hardsigmoid_x86.h", "extension": ".h", "language": "cpp", "size": 1036}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\hardswish_x86.cpp", "relative_path": "x86\\hardswish_x86.cpp", "name": "hardswish_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 3405}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\hardswish_x86.h", "relative_path": "x86\\hardswish_x86.h", "name": "hardswish_x86.h", "extension": ".h", "language": "cpp", "size": 1022}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\innerproduct_fp.h", "relative_path": "x86\\innerproduct_fp.h", "name": "innerproduct_fp.h", "extension": ".h", "language": "cpp", "size": 57564}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\innerproduct_gemm_fp.h", "relative_path": "x86\\innerproduct_gemm_fp.h", "name": "innerproduct_gemm_fp.h", "extension": ".h", "language": "cpp", "size": 52424}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\innerproduct_x86.cpp", "relative_path": "x86\\innerproduct_x86.cpp", "name": "innerproduct_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 27646}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\innerproduct_x86.h", "relative_path": "x86\\innerproduct_x86.h", "name": "innerproduct_x86.h", "extension": ".h", "language": "cpp", "size": 1602}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\innerproduct_x86_f16c.cpp", "relative_path": "x86\\innerproduct_x86_f16c.cpp", "name": "innerproduct_x86_f16c.cpp", "extension": ".cpp", "language": "cpp", "size": 1947}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\interp_bicubic.h", "relative_path": "x86\\interp_bicubic.h", "name": "interp_bicubic.h", "extension": ".h", "language": "cpp", "size": 9856}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\interp_bicubic_pack16.h", "relative_path": "x86\\interp_bicubic_pack16.h", "name": "interp_bicubic_pack16.h", "extension": ".h", "language": "cpp", "size": 11427}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\interp_bicubic_pack4.h", "relative_path": "x86\\interp_bicubic_pack4.h", "name": "interp_bicubic_pack4.h", "extension": ".h", "language": "cpp", "size": 11177}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\interp_bicubic_pack8.h", "relative_path": "x86\\interp_bicubic_pack8.h", "name": "interp_bicubic_pack8.h", "extension": ".h", "language": "cpp", "size": 11544}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\interp_bilinear.h", "relative_path": "x86\\interp_bilinear.h", "name": "interp_bilinear.h", "extension": ".h", "language": "cpp", "size": 4546}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\interp_bilinear_pack16.h", "relative_path": "x86\\interp_bilinear_pack16.h", "name": "interp_bilinear_pack16.h", "extension": ".h", "language": "cpp", "size": 3942}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\interp_bilinear_pack4.h", "relative_path": "x86\\interp_bilinear_pack4.h", "name": "interp_bilinear_pack4.h", "extension": ".h", "language": "cpp", "size": 3868}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\interp_bilinear_pack8.h", "relative_path": "x86\\interp_bilinear_pack8.h", "name": "interp_bilinear_pack8.h", "extension": ".h", "language": "cpp", "size": 3946}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\interp_x86.cpp", "relative_path": "x86\\interp_x86.cpp", "name": "interp_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 29953}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\interp_x86.h", "relative_path": "x86\\interp_x86.h", "name": "interp_x86.h", "extension": ".h", "language": "cpp", "size": 1038}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\layernorm_x86.cpp", "relative_path": "x86\\layernorm_x86.cpp", "name": "layernorm_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 18987}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\layernorm_x86.h", "relative_path": "x86\\layernorm_x86.h", "name": "layernorm_x86.h", "extension": ".h", "language": "cpp", "size": 1022}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\lrn_x86.cpp", "relative_path": "x86\\lrn_x86.cpp", "name": "lrn_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 6250}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\lrn_x86.h", "relative_path": "x86\\lrn_x86.h", "name": "lrn_x86.h", "extension": ".h", "language": "cpp", "size": 964}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\lstm_int8.h", "relative_path": "x86\\lstm_int8.h", "name": "lstm_int8.h", "extension": ".h", "language": "cpp", "size": 156845}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\lstm_x86.cpp", "relative_path": "x86\\lstm_x86.cpp", "name": "lstm_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 34014}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\lstm_x86.h", "relative_path": "x86\\lstm_x86.h", "name": "lstm_x86.h", "extension": ".h", "language": "cpp", "size": 1633}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\lstm_x86_avx2.cpp", "relative_path": "x86\\lstm_x86_avx2.cpp", "name": "lstm_x86_avx2.cpp", "extension": ".cpp", "language": "cpp", "size": 1885}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\lstm_x86_avx512vnni.cpp", "relative_path": "x86\\lstm_x86_avx512vnni.cpp", "name": "lstm_x86_avx512vnni.cpp", "extension": ".cpp", "language": "cpp", "size": 2077}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\lstm_x86_avxvnni.cpp", "relative_path": "x86\\lstm_x86_avxvnni.cpp", "name": "lstm_x86_avxvnni.cpp", "extension": ".cpp", "language": "cpp", "size": 2068}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\lstm_x86_xop.cpp", "relative_path": "x86\\lstm_x86_xop.cpp", "name": "lstm_x86_xop.cpp", "extension": ".cpp", "language": "cpp", "size": 1340}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\matmul_x86.cpp", "relative_path": "x86\\matmul_x86.cpp", "name": "matmul_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 7228}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\matmul_x86.h", "relative_path": "x86\\matmul_x86.h", "name": "matmul_x86.h", "extension": ".h", "language": "cpp", "size": 1170}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\mish_x86.cpp", "relative_path": "x86\\mish_x86.cpp", "name": "mish_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 2172}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\mish_x86.h", "relative_path": "x86\\mish_x86.h", "name": "mish_x86.h", "extension": ".h", "language": "cpp", "size": 987}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\multiheadattention_x86.cpp", "relative_path": "x86\\multiheadattention_x86.cpp", "name": "multiheadattention_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 12675}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\multiheadattention_x86.h", "relative_path": "x86\\multiheadattention_x86.h", "name": "multiheadattention_x86.h", "extension": ".h", "language": "cpp", "size": 1379}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\packing_x86.cpp", "relative_path": "x86\\packing_x86.cpp", "name": "packing_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 60297}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\packing_x86.h", "relative_path": "x86\\packing_x86.h", "name": "packing_x86.h", "extension": ".h", "language": "cpp", "size": 1115}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\padding_pack16.h", "relative_path": "x86\\padding_pack16.h", "name": "padding_pack16.h", "extension": ".h", "language": "cpp", "size": 5874}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\padding_pack4.h", "relative_path": "x86\\padding_pack4.h", "name": "padding_pack4.h", "extension": ".h", "language": "cpp", "size": 5701}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\padding_pack8.h", "relative_path": "x86\\padding_pack8.h", "name": "padding_pack8.h", "extension": ".h", "language": "cpp", "size": 5821}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\padding_pack8_int8.h", "relative_path": "x86\\padding_pack8_int8.h", "name": "padding_pack8_int8.h", "extension": ".h", "language": "cpp", "size": 4224}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\padding_x86.cpp", "relative_path": "x86\\padding_x86.cpp", "name": "padding_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 22902}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\padding_x86.h", "relative_path": "x86\\padding_x86.h", "name": "padding_x86.h", "extension": ".h", "language": "cpp", "size": 1115}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\pooling_2x2.h", "relative_path": "x86\\pooling_2x2.h", "name": "pooling_2x2.h", "extension": ".h", "language": "cpp", "size": 2780}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\pooling_2x2_pack16.h", "relative_path": "x86\\pooling_2x2_pack16.h", "name": "pooling_2x2_pack16.h", "extension": ".h", "language": "cpp", "size": 1983}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\pooling_2x2_pack4.h", "relative_path": "x86\\pooling_2x2_pack4.h", "name": "pooling_2x2_pack4.h", "extension": ".h", "language": "cpp", "size": 1950}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\pooling_2x2_pack8.h", "relative_path": "x86\\pooling_2x2_pack8.h", "name": "pooling_2x2_pack8.h", "extension": ".h", "language": "cpp", "size": 1976}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\pooling_3x3_pack16.h", "relative_path": "x86\\pooling_3x3_pack16.h", "name": "pooling_3x3_pack16.h", "extension": ".h", "language": "cpp", "size": 8063}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\pooling_3x3_pack4.h", "relative_path": "x86\\pooling_3x3_pack4.h", "name": "pooling_3x3_pack4.h", "extension": ".h", "language": "cpp", "size": 4334}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\pooling_3x3_pack8.h", "relative_path": "x86\\pooling_3x3_pack8.h", "name": "pooling_3x3_pack8.h", "extension": ".h", "language": "cpp", "size": 8038}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\pooling_x86.cpp", "relative_path": "x86\\pooling_x86.cpp", "name": "pooling_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 27371}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\pooling_x86.h", "relative_path": "x86\\pooling_x86.h", "name": "pooling_x86.h", "extension": ".h", "language": "cpp", "size": 1102}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\prelu_x86.cpp", "relative_path": "x86\\prelu_x86.cpp", "name": "prelu_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 8802}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\prelu_x86.h", "relative_path": "x86\\prelu_x86.h", "name": "prelu_x86.h", "extension": ".h", "language": "cpp", "size": 994}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\quantize_x86.cpp", "relative_path": "x86\\quantize_x86.cpp", "name": "quantize_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 15934}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\quantize_x86.h", "relative_path": "x86\\quantize_x86.h", "name": "quantize_x86.h", "extension": ".h", "language": "cpp", "size": 1024}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\relu_x86.cpp", "relative_path": "x86\\relu_x86.cpp", "name": "relu_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 6387}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\relu_x86.h", "relative_path": "x86\\relu_x86.h", "name": "relu_x86.h", "extension": ".h", "language": "cpp", "size": 1076}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\requantize_x86.cpp", "relative_path": "x86\\requantize_x86.cpp", "name": "requantize_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 13873}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\requantize_x86.h", "relative_path": "x86\\requantize_x86.h", "name": "requantize_x86.h", "extension": ".h", "language": "cpp", "size": 1038}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\reshape_x86.cpp", "relative_path": "x86\\reshape_x86.cpp", "name": "reshape_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 27409}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\reshape_x86.h", "relative_path": "x86\\reshape_x86.h", "name": "reshape_x86.h", "extension": ".h", "language": "cpp", "size": 1045}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rmsnorm_x86.cpp", "relative_path": "x86\\rmsnorm_x86.cpp", "name": "rmsnorm_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 12559}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rmsnorm_x86.h", "relative_path": "x86\\rmsnorm_x86.h", "name": "rmsnorm_x86.h", "extension": ".h", "language": "cpp", "size": 1008}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\roialign_x86.cpp", "relative_path": "x86\\roialign_x86.cpp", "name": "roialign_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 13304}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\roialign_x86.h", "relative_path": "x86\\roialign_x86.h", "name": "roialign_x86.h", "extension": ".h", "language": "cpp", "size": 1052}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\scale_x86.cpp", "relative_path": "x86\\scale_x86.cpp", "name": "scale_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 12317}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\scale_x86.h", "relative_path": "x86\\scale_x86.h", "name": "scale_x86.h", "extension": ".h", "language": "cpp", "size": 1008}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\selu_x86.cpp", "relative_path": "x86\\selu_x86.cpp", "name": "selu_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 4060}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\selu_x86.h", "relative_path": "x86\\selu_x86.h", "name": "selu_x86.h", "extension": ".h", "language": "cpp", "size": 987}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\shufflechannel_x86.cpp", "relative_path": "x86\\shufflechannel_x86.cpp", "name": "shufflechannel_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 29055}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\shufflechannel_x86.h", "relative_path": "x86\\shufflechannel_x86.h", "name": "shufflechannel_x86.h", "extension": ".h", "language": "cpp", "size": 1066}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\sigmoid_x86.cpp", "relative_path": "x86\\sigmoid_x86.cpp", "name": "sigmoid_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 3275}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\sigmoid_x86.h", "relative_path": "x86\\sigmoid_x86.h", "name": "sigmoid_x86.h", "extension": ".h", "language": "cpp", "size": 1008}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\slice_x86.cpp", "relative_path": "x86\\slice_x86.cpp", "name": "slice_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 32268}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\slice_x86.h", "relative_path": "x86\\slice_x86.h", "name": "slice_x86.h", "extension": ".h", "language": "cpp", "size": 1031}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\softmax_x86.cpp", "relative_path": "x86\\softmax_x86.cpp", "name": "softmax_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 65569}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\softmax_x86.h", "relative_path": "x86\\softmax_x86.h", "name": "softmax_x86.h", "extension": ".h", "language": "cpp", "size": 1008}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\sse_mathfun.h", "relative_path": "x86\\sse_mathfun.h", "name": "sse_mathfun.h", "extension": ".h", "language": "cpp", "size": 42598}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\swish_x86.cpp", "relative_path": "x86\\swish_x86.cpp", "name": "swish_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 2848}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\swish_x86.h", "relative_path": "x86\\swish_x86.h", "name": "swish_x86.h", "extension": ".h", "language": "cpp", "size": 994}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\tanh_x86.cpp", "relative_path": "x86\\tanh_x86.cpp", "name": "tanh_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 2482}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\tanh_x86.h", "relative_path": "x86\\tanh_x86.h", "name": "tanh_x86.h", "extension": ".h", "language": "cpp", "size": 987}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\unaryop_x86.cpp", "relative_path": "x86\\unaryop_x86.cpp", "name": "unaryop_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 17107}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\unaryop_x86.h", "relative_path": "x86\\unaryop_x86.h", "name": "unaryop_x86.h", "extension": ".h", "language": "cpp", "size": 1008}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\x86_activation.h", "relative_path": "x86\\x86_activation.h", "name": "x86_activation.h", "extension": ".h", "language": "cpp", "size": 9485}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\x86_usability.h", "relative_path": "x86\\x86_usability.h", "name": "x86_usability.h", "extension": ".h", "language": "cpp", "size": 85956}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\yolov3detectionoutput_x86.cpp", "relative_path": "x86\\yolov3detectionoutput_x86.cpp", "name": "yolov3detectionoutput_x86.cpp", "extension": ".cpp", "language": "cpp", "size": 7995}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\yolov3detectionoutput_x86.h", "relative_path": "x86\\yolov3detectionoutput_x86.h", "name": "yolov3detectionoutput_x86.h", "extension": ".h", "language": "cpp", "size": 1143}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\batchnorm_riscv.cpp", "relative_path": "x86\\rvv\\batchnorm_riscv.cpp", "name": "batchnorm_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 7027}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\batchnorm_riscv.h", "relative_path": "x86\\rvv\\batchnorm_riscv.h", "name": "batchnorm_riscv.h", "extension": ".h", "language": "cpp", "size": 1008}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\batch_create_operators.py", "relative_path": "x86\\rvv\\batch_create_operators.py", "name": "batch_create_operators.py", "extension": ".py", "language": "python", "size": 8092}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\bias_riscv.cpp", "relative_path": "x86\\rvv\\bias_riscv.cpp", "name": "bias_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 1911}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\bias_riscv.h", "relative_path": "x86\\rvv\\bias_riscv.h", "name": "bias_riscv.h", "extension": ".h", "language": "cpp", "size": 978}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\binaryop_riscv.cpp", "relative_path": "x86\\rvv\\binaryop_riscv.cpp", "name": "binaryop_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 13887}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\binaryop_riscv.h", "relative_path": "x86\\rvv\\binaryop_riscv.h", "name": "binaryop_riscv.h", "extension": ".h", "language": "cpp", "size": 1363}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\bnll_riscv.cpp", "relative_path": "x86\\rvv\\bnll_riscv.cpp", "name": "bnll_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2086}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\bnll_riscv.h", "relative_path": "x86\\rvv\\bnll_riscv.h", "name": "bnll_riscv.h", "extension": ".h", "language": "cpp", "size": 1006}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\cast_riscv.cpp", "relative_path": "x86\\rvv\\cast_riscv.cpp", "name": "cast_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2050}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\cast_riscv.h", "relative_path": "x86\\rvv\\cast_riscv.h", "name": "cast_riscv.h", "extension": ".h", "language": "cpp", "size": 1006}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\clip_riscv.cpp", "relative_path": "x86\\rvv\\clip_riscv.cpp", "name": "clip_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2195}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\clip_riscv.h", "relative_path": "x86\\rvv\\clip_riscv.h", "name": "clip_riscv.h", "extension": ".h", "language": "cpp", "size": 978}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\concat_riscv.cpp", "relative_path": "x86\\rvv\\concat_riscv.cpp", "name": "concat_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2056}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\concat_riscv.h", "relative_path": "x86\\rvv\\concat_riscv.h", "name": "concat_riscv.h", "extension": ".h", "language": "cpp", "size": 1018}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\convolution1d_riscv.cpp", "relative_path": "x86\\rvv\\convolution1d_riscv.cpp", "name": "convolution1d_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 5136}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\convolution1d_riscv.h", "relative_path": "x86\\rvv\\convolution1d_riscv.h", "name": "convolution1d_riscv.h", "extension": ".h", "language": "cpp", "size": 1150}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\convolutiondepthwise_riscv.cpp", "relative_path": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "name": "convolutiondepthwise_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2100}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\convolutiondepthwise_riscv.h", "relative_path": "x86\\rvv\\convolutiondepthwise_riscv.h", "name": "convolutiondepthwise_riscv.h", "extension": ".h", "language": "cpp", "size": 1102}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\convolution_riscv.cpp", "relative_path": "x86\\rvv\\convolution_riscv.cpp", "name": "convolution_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2062}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\convolution_riscv.h", "relative_path": "x86\\rvv\\convolution_riscv.h", "name": "convolution_riscv.h", "extension": ".h", "language": "cpp", "size": 1048}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\crop_riscv.cpp", "relative_path": "x86\\rvv\\crop_riscv.cpp", "name": "crop_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2064}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\crop_riscv.h", "relative_path": "x86\\rvv\\crop_riscv.h", "name": "crop_riscv.h", "extension": ".h", "language": "cpp", "size": 1006}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "relative_path": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "name": "deconvolutiondepthwise_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2108}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\deconvolutiondepthwise_riscv.h", "relative_path": "x86\\rvv\\deconvolutiondepthwise_riscv.h", "name": "deconvolutiondepthwise_riscv.h", "extension": ".h", "language": "cpp", "size": 1114}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\deconvolution_riscv.cpp", "relative_path": "x86\\rvv\\deconvolution_riscv.cpp", "name": "deconvolution_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2070}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\deconvolution_riscv.h", "relative_path": "x86\\rvv\\deconvolution_riscv.h", "name": "deconvolution_riscv.h", "extension": ".h", "language": "cpp", "size": 1060}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\deformableconv2d_riscv.cpp", "relative_path": "x86\\rvv\\deformableconv2d_riscv.cpp", "name": "deformableconv2d_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2094}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\deformableconv2d_riscv.h", "relative_path": "x86\\rvv\\deformableconv2d_riscv.h", "name": "deformableconv2d_riscv.h", "extension": ".h", "language": "cpp", "size": 1078}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\dequantize_riscv.cpp", "relative_path": "x86\\rvv\\dequantize_riscv.cpp", "name": "dequantize_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2066}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\dequantize_riscv.h", "relative_path": "x86\\rvv\\dequantize_riscv.h", "name": "dequantize_riscv.h", "extension": ".h", "language": "cpp", "size": 1042}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\dropout_riscv.cpp", "relative_path": "x86\\rvv\\dropout_riscv.cpp", "name": "dropout_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 4798}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\dropout_riscv.h", "relative_path": "x86\\rvv\\dropout_riscv.h", "name": "dropout_riscv.h", "extension": ".h", "language": "cpp", "size": 1114}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\eltwise_riscv.cpp", "relative_path": "x86\\rvv\\eltwise_riscv.cpp", "name": "eltwise_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2078}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\eltwise_riscv.h", "relative_path": "x86\\rvv\\eltwise_riscv.h", "name": "eltwise_riscv.h", "extension": ".h", "language": "cpp", "size": 1024}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\elu_riscv.cpp", "relative_path": "x86\\rvv\\elu_riscv.cpp", "name": "elu_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 3795}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\elu_riscv.h", "relative_path": "x86\\rvv\\elu_riscv.h", "name": "elu_riscv.h", "extension": ".h", "language": "cpp", "size": 972}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\flatten_riscv.cpp", "relative_path": "x86\\rvv\\flatten_riscv.cpp", "name": "flatten_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2066}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\flatten_riscv.h", "relative_path": "x86\\rvv\\flatten_riscv.h", "name": "flatten_riscv.h", "extension": ".h", "language": "cpp", "size": 1024}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\gelu_riscv.cpp", "relative_path": "x86\\rvv\\gelu_riscv.cpp", "name": "gelu_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2909}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\gelu_riscv.h", "relative_path": "x86\\rvv\\gelu_riscv.h", "name": "gelu_riscv.h", "extension": ".h", "language": "cpp", "size": 978}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\gemm_riscv.cpp", "relative_path": "x86\\rvv\\gemm_riscv.cpp", "name": "gemm_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2072}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\gemm_riscv.h", "relative_path": "x86\\rvv\\gemm_riscv.h", "name": "gemm_riscv.h", "extension": ".h", "language": "cpp", "size": 1006}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\gridsample_riscv.cpp", "relative_path": "x86\\rvv\\gridsample_riscv.cpp", "name": "gridsample_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2064}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\gridsample_riscv.h", "relative_path": "x86\\rvv\\gridsample_riscv.h", "name": "gridsample_riscv.h", "extension": ".h", "language": "cpp", "size": 1042}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\groupnorm_riscv.cpp", "relative_path": "x86\\rvv\\groupnorm_riscv.cpp", "name": "groupnorm_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2074}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\groupnorm_riscv.h", "relative_path": "x86\\rvv\\groupnorm_riscv.h", "name": "groupnorm_riscv.h", "extension": ".h", "language": "cpp", "size": 1036}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\hardsigmoid_riscv.cpp", "relative_path": "x86\\rvv\\hardsigmoid_riscv.cpp", "name": "hardsigmoid_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2633}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\hardsigmoid_riscv.h", "relative_path": "x86\\rvv\\hardsigmoid_riscv.h", "name": "hardsigmoid_riscv.h", "extension": ".h", "language": "cpp", "size": 1020}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\hardswish_riscv.cpp", "relative_path": "x86\\rvv\\hardswish_riscv.cpp", "name": "hardswish_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2961}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\hardswish_riscv.h", "relative_path": "x86\\rvv\\hardswish_riscv.h", "name": "hardswish_riscv.h", "extension": ".h", "language": "cpp", "size": 1008}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\innerproduct_riscv.cpp", "relative_path": "x86\\rvv\\innerproduct_riscv.cpp", "name": "innerproduct_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2068}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\innerproduct_riscv.h", "relative_path": "x86\\rvv\\innerproduct_riscv.h", "name": "innerproduct_riscv.h", "extension": ".h", "language": "cpp", "size": 1054}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\interp_riscv.cpp", "relative_path": "x86\\rvv\\interp_riscv.cpp", "name": "interp_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2056}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\interp_riscv.h", "relative_path": "x86\\rvv\\interp_riscv.h", "name": "interp_riscv.h", "extension": ".h", "language": "cpp", "size": 1018}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\layernorm_riscv.cpp", "relative_path": "x86\\rvv\\layernorm_riscv.cpp", "name": "layernorm_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 5077}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\layernorm_riscv.h", "relative_path": "x86\\rvv\\layernorm_riscv.h", "name": "layernorm_riscv.h", "extension": ".h", "language": "cpp", "size": 1008}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\lrn_riscv.cpp", "relative_path": "x86\\rvv\\lrn_riscv.cpp", "name": "lrn_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2080}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\lrn_riscv.h", "relative_path": "x86\\rvv\\lrn_riscv.h", "name": "lrn_riscv.h", "extension": ".h", "language": "cpp", "size": 1000}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\lstm_riscv.cpp", "relative_path": "x86\\rvv\\lstm_riscv.cpp", "name": "lstm_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2034}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\lstm_riscv.h", "relative_path": "x86\\rvv\\lstm_riscv.h", "name": "lstm_riscv.h", "extension": ".h", "language": "cpp", "size": 1006}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\matmul_riscv.cpp", "relative_path": "x86\\rvv\\matmul_riscv.cpp", "name": "matmul_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2072}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\matmul_riscv.h", "relative_path": "x86\\rvv\\matmul_riscv.h", "name": "matmul_riscv.h", "extension": ".h", "language": "cpp", "size": 1018}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\mish_riscv.cpp", "relative_path": "x86\\rvv\\mish_riscv.cpp", "name": "mish_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 3649}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\mish_riscv.h", "relative_path": "x86\\rvv\\mish_riscv.h", "name": "mish_riscv.h", "extension": ".h", "language": "cpp", "size": 978}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\multiheadattention_riscv.cpp", "relative_path": "x86\\rvv\\multiheadattention_riscv.cpp", "name": "multiheadattention_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2094}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\multiheadattention_riscv.h", "relative_path": "x86\\rvv\\multiheadattention_riscv.h", "name": "multiheadattention_riscv.h", "extension": ".h", "language": "cpp", "size": 1090}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\packing_riscv.cpp", "relative_path": "x86\\rvv\\packing_riscv.cpp", "name": "packing_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2056}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\packing_riscv.h", "relative_path": "x86\\rvv\\packing_riscv.h", "name": "packing_riscv.h", "extension": ".h", "language": "cpp", "size": 1024}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\padding_riscv.cpp", "relative_path": "x86\\rvv\\padding_riscv.cpp", "name": "padding_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2068}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\padding_riscv.h", "relative_path": "x86\\rvv\\padding_riscv.h", "name": "padding_riscv.h", "extension": ".h", "language": "cpp", "size": 1024}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\pooling_riscv.cpp", "relative_path": "x86\\rvv\\pooling_riscv.cpp", "name": "pooling_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 7463}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\pooling_riscv.h", "relative_path": "x86\\rvv\\pooling_riscv.h", "name": "pooling_riscv.h", "extension": ".h", "language": "cpp", "size": 1187}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\prelu_riscv.cpp", "relative_path": "x86\\rvv\\prelu_riscv.cpp", "name": "prelu_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2058}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\prelu_riscv.h", "relative_path": "x86\\rvv\\prelu_riscv.h", "name": "prelu_riscv.h", "extension": ".h", "language": "cpp", "size": 1012}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\quantize_riscv.cpp", "relative_path": "x86\\rvv\\quantize_riscv.cpp", "name": "quantize_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2058}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\quantize_riscv.h", "relative_path": "x86\\rvv\\quantize_riscv.h", "name": "quantize_riscv.h", "extension": ".h", "language": "cpp", "size": 1030}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\relu_riscv.cpp", "relative_path": "x86\\rvv\\relu_riscv.cpp", "name": "relu_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 3408}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\relu_riscv.h", "relative_path": "x86\\rvv\\relu_riscv.h", "name": "relu_riscv.h", "extension": ".h", "language": "cpp", "size": 997}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\requantize_riscv.cpp", "relative_path": "x86\\rvv\\requantize_riscv.cpp", "name": "requantize_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2066}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\requantize_riscv.h", "relative_path": "x86\\rvv\\requantize_riscv.h", "name": "requantize_riscv.h", "extension": ".h", "language": "cpp", "size": 1042}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\reshape_riscv.cpp", "relative_path": "x86\\rvv\\reshape_riscv.cpp", "name": "reshape_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2064}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\reshape_riscv.h", "relative_path": "x86\\rvv\\reshape_riscv.h", "name": "reshape_riscv.h", "extension": ".h", "language": "cpp", "size": 1024}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\riscv_usability.h", "relative_path": "x86\\rvv\\riscv_usability.h", "name": "riscv_usability.h", "extension": ".h", "language": "cpp", "size": 6921}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\rmsnorm_riscv.cpp", "relative_path": "x86\\rvv\\rmsnorm_riscv.cpp", "name": "rmsnorm_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2066}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\rmsnorm_riscv.h", "relative_path": "x86\\rvv\\rmsnorm_riscv.h", "name": "rmsnorm_riscv.h", "extension": ".h", "language": "cpp", "size": 1024}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\roialign_riscv.cpp", "relative_path": "x86\\rvv\\roialign_riscv.cpp", "name": "roialign_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2052}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\roialign_riscv.h", "relative_path": "x86\\rvv\\roialign_riscv.h", "name": "roialign_riscv.h", "extension": ".h", "language": "cpp", "size": 1030}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\rvv_mathfun.h", "relative_path": "x86\\rvv\\rvv_mathfun.h", "name": "rvv_mathfun.h", "extension": ".h", "language": "cpp", "size": 9791}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\scale_riscv.cpp", "relative_path": "x86\\rvv\\scale_riscv.cpp", "name": "scale_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 5310}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\scale_riscv.h", "relative_path": "x86\\rvv\\scale_riscv.h", "name": "scale_riscv.h", "extension": ".h", "language": "cpp", "size": 1111}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\selu_riscv.cpp", "relative_path": "x86\\rvv\\selu_riscv.cpp", "name": "selu_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 3739}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\selu_riscv.h", "relative_path": "x86\\rvv\\selu_riscv.h", "name": "selu_riscv.h", "extension": ".h", "language": "cpp", "size": 978}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\shufflechannel_riscv.cpp", "relative_path": "x86\\rvv\\shufflechannel_riscv.cpp", "name": "shufflechannel_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2080}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\shufflechannel_riscv.h", "relative_path": "x86\\rvv\\shufflechannel_riscv.h", "name": "shufflechannel_riscv.h", "extension": ".h", "language": "cpp", "size": 1066}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\sigmoid_riscv.cpp", "relative_path": "x86\\rvv\\sigmoid_riscv.cpp", "name": "sigmoid_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2982}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\sigmoid_riscv.h", "relative_path": "x86\\rvv\\sigmoid_riscv.h", "name": "sigmoid_riscv.h", "extension": ".h", "language": "cpp", "size": 996}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\slice_riscv.cpp", "relative_path": "x86\\rvv\\slice_riscv.cpp", "name": "slice_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2056}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\slice_riscv.h", "relative_path": "x86\\rvv\\slice_riscv.h", "name": "slice_riscv.h", "extension": ".h", "language": "cpp", "size": 1012}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\softmax_riscv.cpp", "relative_path": "x86\\rvv\\softmax_riscv.cpp", "name": "softmax_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 11179}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\softmax_riscv.h", "relative_path": "x86\\rvv\\softmax_riscv.h", "name": "softmax_riscv.h", "extension": ".h", "language": "cpp", "size": 996}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\swish_riscv.cpp", "relative_path": "x86\\rvv\\swish_riscv.cpp", "name": "swish_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2564}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\swish_riscv.h", "relative_path": "x86\\rvv\\swish_riscv.h", "name": "swish_riscv.h", "extension": ".h", "language": "cpp", "size": 984}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\tanh_riscv.cpp", "relative_path": "x86\\rvv\\tanh_riscv.cpp", "name": "tanh_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 3044}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\tanh_riscv.h", "relative_path": "x86\\rvv\\tanh_riscv.h", "name": "tanh_riscv.h", "extension": ".h", "language": "cpp", "size": 978}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\test_riscv_operators.cpp", "relative_path": "x86\\rvv\\test_riscv_operators.cpp", "name": "test_riscv_operators.cpp", "extension": ".cpp", "language": "cpp", "size": 11995}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\unaryop_riscv.cpp", "relative_path": "x86\\rvv\\unaryop_riscv.cpp", "name": "unaryop_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2064}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\unaryop_riscv.h", "relative_path": "x86\\rvv\\unaryop_riscv.h", "name": "unaryop_riscv.h", "extension": ".h", "language": "cpp", "size": 1024}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\yolov3detectionoutput_riscv.cpp", "relative_path": "x86\\rvv\\yolov3detectionoutput_riscv.cpp", "name": "yolov3detectionoutput_riscv.cpp", "extension": ".cpp", "language": "cpp", "size": 2108}, {"path": "C:\\Users\\<USER>\\Desktop\\x86\\x86\\rvv\\yolov3detectionoutput_riscv.h", "relative_path": "x86\\rvv\\yolov3detectionoutput_riscv.h", "name": "yolov3detectionoutput_riscv.h", "extension": ".h", "language": "cpp", "size": 1108}], "dependencies": [{"source": "x86\\batchnorm_x86.cpp", "target": "x86\\batchnorm_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\batchnorm_x86.cpp", "target": "x86\\batchnorm_x86.h", "type": "import", "line": 15}, {"source": "x86\\batchnorm_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 23}, {"source": "x86\\bias_x86.cpp", "target": "x86\\rvv\\bias_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\bias_x86.cpp", "target": "x86\\rvv\\bias_riscv.h", "type": "import", "line": 15}, {"source": "x86\\binaryop_x86.cpp", "target": "x86\\binaryop_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\binaryop_x86.cpp", "target": "x86\\binaryop_x86.h", "type": "import", "line": 15}, {"source": "x86\\bnll_x86.cpp", "target": "x86\\bnll_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\bnll_x86.cpp", "target": "x86\\bnll_x86.h", "type": "import", "line": 15}, {"source": "x86\\cast_x86.cpp", "target": "x86\\cast_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\cast_x86.cpp", "target": "x86\\cast_x86.h", "type": "import", "line": 15}, {"source": "x86\\cast_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 23}, {"source": "x86\\cast_x86_avx2.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 17}, {"source": "x86\\cast_x86_avx512bf16.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 17}, {"source": "x86\\cast_x86_f16c.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 17}, {"source": "x86\\clip_x86.cpp", "target": "x86\\clip_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\clip_x86.cpp", "target": "x86\\clip_x86.h", "type": "import", "line": 15}, {"source": "x86\\concat_x86.cpp", "target": "x86\\concat_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\concat_x86.cpp", "target": "x86\\concat_x86.h", "type": "import", "line": 15}, {"source": "x86\\convolution1d_x86.cpp", "target": "x86\\convolution1d_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\convolution1d_x86.cpp", "target": "x86\\convolution1d_x86.h", "type": "import", "line": 15}, {"source": "x86\\convolution1d_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 23}, {"source": "x86\\convolution1d_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 24}, {"source": "x86\\convolutiondepthwise_x86.cpp", "target": "x86\\convolutiondepthwise_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\convolutiondepthwise_x86.cpp", "target": "x86\\convolutiondepthwise_x86.h", "type": "import", "line": 15}, {"source": "x86\\convolutiondepthwise_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 24}, {"source": "x86\\convolutiondepthwise_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 25}, {"source": "x86\\convolution_x86.cpp", "target": "x86\\convolution_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\convolution_x86.cpp", "target": "x86\\convolution_x86.h", "type": "import", "line": 15}, {"source": "x86\\convolution_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 29}, {"source": "x86\\convolution_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 30}, {"source": "x86\\convolution_x86_avx2.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 17}, {"source": "x86\\convolution_x86_avx512vnni.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 17}, {"source": "x86\\convolution_x86_avxvnni.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 17}, {"source": "x86\\convolution_x86_avxvnniint8.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 17}, {"source": "x86\\convolution_x86_xop.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 17}, {"source": "x86\\crop_x86.cpp", "target": "x86\\crop_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\crop_x86.cpp", "target": "x86\\crop_x86.h", "type": "import", "line": 15}, {"source": "x86\\deconvolutiondepthwise_x86.cpp", "target": "x86\\convolutiondepthwise_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\deconvolutiondepthwise_x86.cpp", "target": "x86\\convolutiondepthwise_x86.h", "type": "import", "line": 15}, {"source": "x86\\deconvolutiondepthwise_x86.cpp", "target": "x86\\deconvolutiondepthwise_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\deconvolutiondepthwise_x86.cpp", "target": "x86\\deconvolutiondepthwise_x86.h", "type": "import", "line": 15}, {"source": "x86\\deconvolutiondepthwise_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 26}, {"source": "x86\\deconvolutiondepthwise_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 27}, {"source": "x86\\deconvolution_x86.cpp", "target": "x86\\convolution_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\deconvolution_x86.cpp", "target": "x86\\convolution_x86.h", "type": "import", "line": 15}, {"source": "x86\\deconvolution_x86.cpp", "target": "x86\\deconvolution_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\deconvolution_x86.cpp", "target": "x86\\deconvolution_x86.h", "type": "import", "line": 15}, {"source": "x86\\deconvolution_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 26}, {"source": "x86\\deconvolution_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 27}, {"source": "x86\\deformableconv2d_x86.cpp", "target": "x86\\deformableconv2d_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\deformableconv2d_x86.cpp", "target": "x86\\deformableconv2d_x86.h", "type": "import", "line": 15}, {"source": "x86\\deformableconv2d_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 26}, {"source": "x86\\deformableconv2d_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 27}, {"source": "x86\\dequantize_x86.cpp", "target": "x86\\dequantize_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\dequantize_x86.cpp", "target": "x86\\dequantize_x86.h", "type": "import", "line": 15}, {"source": "x86\\dequantize_x86.cpp", "target": "x86\\quantize_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\dequantize_x86.cpp", "target": "x86\\quantize_x86.h", "type": "import", "line": 15}, {"source": "x86\\dequantize_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 24}, {"source": "x86\\dropout_x86.cpp", "target": "x86\\dropout_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\dropout_x86.cpp", "target": "x86\\dropout_x86.h", "type": "import", "line": 15}, {"source": "x86\\eltwise_x86.cpp", "target": "x86\\eltwise_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\eltwise_x86.cpp", "target": "x86\\eltwise_x86.h", "type": "import", "line": 15}, {"source": "x86\\eltwise_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 23}, {"source": "x86\\elu_x86.cpp", "target": "x86\\elu_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\elu_x86.cpp", "target": "x86\\elu_x86.h", "type": "import", "line": 15}, {"source": "x86\\elu_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 17}, {"source": "x86\\flatten_x86.cpp", "target": "x86\\flatten_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\flatten_x86.cpp", "target": "x86\\flatten_x86.h", "type": "import", "line": 15}, {"source": "x86\\flatten_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 24}, {"source": "x86\\gelu_x86.cpp", "target": "x86\\elu_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\gelu_x86.cpp", "target": "x86\\elu_x86.h", "type": "import", "line": 15}, {"source": "x86\\gelu_x86.cpp", "target": "x86\\gelu_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\gelu_x86.cpp", "target": "x86\\gelu_x86.h", "type": "import", "line": 15}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_int8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_1x1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_int8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd_int8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_5x5.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm_int8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx2.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx512vnni.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnni.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnniint8.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_xop.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_int8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avx512vnni.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnni.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnniint8.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\generate_riscv_operators.py", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_apply_interpolation.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_compute_blob.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_apply_interpolation.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_compute_blob.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_compute_blob.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_apply_interpolation.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_compute_blob.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_fp.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_gemm_fp.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86_f16c.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_int8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avx512vnni.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avxvnni.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_activation.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_usability.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\riscv_usability.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\test_riscv_operators.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to1.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 68}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_int8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_1x1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_int8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd_int8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_5x5.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm_int8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx2.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx512vnni.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnni.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnniint8.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_xop.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_int8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avx512vnni.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnni.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnniint8.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\generate_riscv_operators.py", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_apply_interpolation.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_compute_blob.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_apply_interpolation.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_compute_blob.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_compute_blob.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_apply_interpolation.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_compute_blob.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_fp.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_gemm_fp.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86_f16c.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_int8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avx512vnni.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avxvnni.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_activation.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_usability.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\riscv_usability.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\test_riscv_operators.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to1.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 416}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to1.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to1.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to1.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 756}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to1.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to1.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to1.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 1118}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_int8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_1x1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_int8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd_int8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_5x5.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm_int8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx2.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx512vnni.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnni.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnniint8.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_xop.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_int8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avx512vnni.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnni.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnniint8.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\generate_riscv_operators.py", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_apply_interpolation.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_compute_blob.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_apply_interpolation.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_compute_blob.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_compute_blob.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_apply_interpolation.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_compute_blob.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_fp.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_gemm_fp.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86_f16c.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_int8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avx512vnni.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avxvnni.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_activation.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_usability.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\riscv_usability.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\test_riscv_operators.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to1.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 1567}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_int8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_1x1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_int8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd_int8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_5x5.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm_int8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx2.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx512vnni.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnni.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnniint8.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_xop.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_int8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avx512vnni.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnni.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnniint8.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\generate_riscv_operators.py", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_apply_interpolation.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_compute_blob.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_apply_interpolation.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_compute_blob.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_compute_blob.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_apply_interpolation.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_compute_blob.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_fp.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_gemm_fp.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86_f16c.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_int8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avx512vnni.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avxvnni.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_activation.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_usability.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\riscv_usability.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\test_riscv_operators.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to1.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 3345}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to1.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to1.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to1.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 4987}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to1.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to1.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to1.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 6060}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_int8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_1x1.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_int8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd_int8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_5x5.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm_int8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx2.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx512vnni.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnni.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnniint8.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_xop.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_int8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avx512vnni.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnni.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnniint8.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\generate_riscv_operators.py", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_apply_interpolation.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_compute_blob.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_apply_interpolation.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_compute_blob.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_compute_blob.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_apply_interpolation.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_compute_blob.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_fp.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_gemm_fp.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86_f16c.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_int8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avx512vnni.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avxvnni.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_activation.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_usability.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\riscv_usability.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\test_riscv_operators.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.cpp", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.h", "type": "reference", "line": 7472}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\bias_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\binaryop_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\clip_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_int8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_1x1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_int8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_winograd_int8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_5x5.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_im2col_gemm_int8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx2.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avx512vnni.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnni.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_avxvnniint8.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_x86_xop.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolutiondepthwise_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\dequantize_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\eltwise_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_int8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avx512vnni.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnni.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gemm_x86_avxvnniint8.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\generate_riscv_operators.py", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_apply_interpolation.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bicubic_compute_blob.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_apply_interpolation.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_bilinear_compute_blob.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_compute_blob.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_apply_interpolation.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_nearest_compute_blob.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\gridsample_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardsigmoid_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\hardswish_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_fp.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_gemm_fp.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\innerproduct_x86_f16c.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_int8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avx512vnni.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\lstm_x86_avxvnni.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\mish_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\multiheadattention_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\quantize_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\requantize_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\roialign_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\sigmoid_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\slice_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\swish_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_activation.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\x86_usability.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\yolov3detectionoutput_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\batchnorm_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bias_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\binaryop_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\bnll_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\cast_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\clip_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\concat_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution1d_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\convolution_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\crop_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deconvolution_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\deformableconv2d_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dequantize_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\dropout_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\eltwise_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\elu_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\flatten_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gelu_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gemm_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\gridsample_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\groupnorm_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardsigmoid_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\hardswish_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\innerproduct_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\interp_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\layernorm_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lrn_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\lstm_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\matmul_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\mish_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\multiheadattention_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\padding_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\pooling_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\prelu_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\quantize_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\relu_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\requantize_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\reshape_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\riscv_usability.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\rmsnorm_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\roialign_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\scale_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\selu_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\shufflechannel_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\sigmoid_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\slice_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\softmax_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\swish_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\tanh_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\test_riscv_operators.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\unaryop_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\yolov3detectionoutput_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution1d_packed.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_2x2_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack16to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack1to8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_3x3_pack8to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\convolution_packed_int8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack16to8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack1to8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack4to8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deconvolution_pack8to4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack16to8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack1to8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack4to8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to1.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\deformableconv2d_pack8to4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bicubic_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\interp_bilinear_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\packing_x86.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\padding_pack8_int8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_2x2_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack16.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack4.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\pooling_3x3_pack8.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.cpp", "type": "reference", "line": 12483}, {"source": "x86\\gemm_int8.h", "target": "x86\\rvv\\packing_riscv.h", "type": "reference", "line": 12483}, {"source": "x86\\gemm_x86.cpp", "target": "x86\\gemm_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\gemm_x86.cpp", "target": "x86\\gemm_x86.h", "type": "import", "line": 15}, {"source": "x86\\gemm_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 28}, {"source": "x86\\gemm_x86_avx2.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 25}, {"source": "x86\\gemm_x86_avx512vnni.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 28}, {"source": "x86\\gemm_x86_avxvnni.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 25}, {"source": "x86\\gemm_x86_avxvnniint8.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 25}, {"source": "x86\\gemm_x86_xop.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 25}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\gridsample_nearest_apply_interpolation.h", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\gridsample_nearest_compute_blob.h", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\prelu_x86.cpp", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\prelu_x86.h", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\relu_x86.cpp", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\relu_x86.h", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\requantize_x86.cpp", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\requantize_x86.h", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\reshape_x86.cpp", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\reshape_x86.h", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\rvv\\batch_create_operators.py", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\rvv\\prelu_riscv.cpp", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\rvv\\prelu_riscv.h", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\rvv\\relu_riscv.cpp", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\rvv\\relu_riscv.h", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\rvv\\requantize_riscv.cpp", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\rvv\\requantize_riscv.h", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\rvv\\reshape_riscv.cpp", "type": "import", "line": 8}, {"source": "x86\\generate_riscv_operators.py", "target": "x86\\rvv\\reshape_riscv.h", "type": "import", "line": 8}, {"source": "x86\\gridsample_compute_blob.h", "target": "x86\\x86_usability.h", "type": "import", "line": 15}, {"source": "x86\\gridsample_compute_blob.h", "target": "x86\\gridsample_bilinear_compute_blob.h", "type": "import", "line": 143}, {"source": "x86\\gridsample_compute_blob.h", "target": "x86\\gridsample_bicubic_compute_blob.h", "type": "import", "line": 144}, {"source": "x86\\gridsample_compute_blob.h", "target": "x86\\gridsample_nearest_compute_blob.h", "type": "import", "line": 145}, {"source": "x86\\gridsample_x86.cpp", "target": "x86\\gridsample_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\gridsample_x86.cpp", "target": "x86\\gridsample_x86.h", "type": "import", "line": 15}, {"source": "x86\\gridsample_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 28}, {"source": "x86\\groupnorm_x86.cpp", "target": "x86\\groupnorm_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\groupnorm_x86.cpp", "target": "x86\\groupnorm_x86.h", "type": "import", "line": 15}, {"source": "x86\\hardsigmoid_x86.cpp", "target": "x86\\hardsigmoid_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\hardsigmoid_x86.cpp", "target": "x86\\hardsigmoid_x86.h", "type": "import", "line": 15}, {"source": "x86\\hardsigmoid_x86.cpp", "target": "x86\\sigmoid_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\hardsigmoid_x86.cpp", "target": "x86\\sigmoid_x86.h", "type": "import", "line": 15}, {"source": "x86\\hardsigmoid_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 24}, {"source": "x86\\hardswish_x86.cpp", "target": "x86\\hardswish_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\hardswish_x86.cpp", "target": "x86\\hardswish_x86.h", "type": "import", "line": 15}, {"source": "x86\\hardswish_x86.cpp", "target": "x86\\swish_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\hardswish_x86.cpp", "target": "x86\\swish_x86.h", "type": "import", "line": 15}, {"source": "x86\\hardswish_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 24}, {"source": "x86\\innerproduct_x86.cpp", "target": "x86\\innerproduct_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\innerproduct_x86.cpp", "target": "x86\\innerproduct_x86.h", "type": "import", "line": 15}, {"source": "x86\\innerproduct_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 24}, {"source": "x86\\innerproduct_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 25}, {"source": "x86\\innerproduct_x86_f16c.cpp", "target": "x86\\innerproduct_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\innerproduct_x86_f16c.cpp", "target": "x86\\innerproduct_x86.h", "type": "import", "line": 15}, {"source": "x86\\innerproduct_x86_f16c.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 24}, {"source": "x86\\innerproduct_x86_f16c.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 25}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolutiondepthwise_3x3.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolutiondepthwise_3x3_int8.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolutiondepthwise_x86.cpp", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolutiondepthwise_x86.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolution_3x3_winograd.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\convolution_3x3_winograd_int8.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\deconvolutiondepthwise_x86.cpp", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\deconvolutiondepthwise_x86.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\eltwise_x86.cpp", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\eltwise_x86.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\hardswish_x86.cpp", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\hardswish_x86.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\swish_x86.cpp", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\swish_x86.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\rvv\\eltwise_riscv.cpp", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\rvv\\eltwise_riscv.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\rvv\\hardswish_riscv.cpp", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\rvv\\hardswish_riscv.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\rvv\\swish_riscv.cpp", "type": "reference", "line": 30}, {"source": "x86\\interp_bicubic.h", "target": "x86\\rvv\\swish_riscv.h", "type": "reference", "line": 30}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolutiondepthwise_3x3.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolutiondepthwise_3x3_int8.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolutiondepthwise_3x3_pack16.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolutiondepthwise_3x3_pack4.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolutiondepthwise_3x3_pack8.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolutiondepthwise_5x5_pack16.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolutiondepthwise_5x5_pack4.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolutiondepthwise_5x5_pack8.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolutiondepthwise_x86.cpp", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolutiondepthwise_x86.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolution_3x3_winograd.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\convolution_3x3_winograd_int8.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\deconvolutiondepthwise_x86.cpp", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\deconvolutiondepthwise_x86.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\eltwise_x86.cpp", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\eltwise_x86.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\hardswish_x86.cpp", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\hardswish_x86.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\swish_x86.cpp", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\swish_x86.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\rvv\\convolutiondepthwise_riscv.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\rvv\\eltwise_riscv.cpp", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\rvv\\eltwise_riscv.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\rvv\\hardswish_riscv.cpp", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\rvv\\hardswish_riscv.h", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\rvv\\swish_riscv.cpp", "type": "reference", "line": 15}, {"source": "x86\\interp_bilinear.h", "target": "x86\\rvv\\swish_riscv.h", "type": "reference", "line": 15}, {"source": "x86\\interp_x86.cpp", "target": "x86\\interp_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\interp_x86.cpp", "target": "x86\\interp_x86.h", "type": "import", "line": 15}, {"source": "x86\\interp_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 24}, {"source": "x86\\layernorm_x86.cpp", "target": "x86\\layernorm_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\layernorm_x86.cpp", "target": "x86\\layernorm_x86.h", "type": "import", "line": 15}, {"source": "x86\\layernorm_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 24}, {"source": "x86\\lrn_x86.cpp", "target": "x86\\lrn_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\lrn_x86.cpp", "target": "x86\\lrn_x86.h", "type": "import", "line": 15}, {"source": "x86\\lstm_x86.cpp", "target": "x86\\lstm_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\lstm_x86.cpp", "target": "x86\\lstm_x86.h", "type": "import", "line": 15}, {"source": "x86\\lstm_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 29}, {"source": "x86\\lstm_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 30}, {"source": "x86\\lstm_x86_avx2.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 18}, {"source": "x86\\lstm_x86_avx2.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 19}, {"source": "x86\\lstm_x86_avx512vnni.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 18}, {"source": "x86\\lstm_x86_avx512vnni.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 19}, {"source": "x86\\lstm_x86_avxvnni.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 18}, {"source": "x86\\lstm_x86_avxvnni.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 19}, {"source": "x86\\lstm_x86_xop.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 18}, {"source": "x86\\lstm_x86_xop.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 19}, {"source": "x86\\matmul_x86.cpp", "target": "x86\\matmul_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\matmul_x86.cpp", "target": "x86\\matmul_x86.h", "type": "import", "line": 15}, {"source": "x86\\mish_x86.cpp", "target": "x86\\mish_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\mish_x86.cpp", "target": "x86\\mish_x86.h", "type": "import", "line": 15}, {"source": "x86\\mish_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 17}, {"source": "x86\\multiheadattention_x86.cpp", "target": "x86\\multiheadattention_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\multiheadattention_x86.cpp", "target": "x86\\multiheadattention_x86.h", "type": "import", "line": 15}, {"source": "x86\\packing_x86.cpp", "target": "x86\\packing_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\packing_x86.cpp", "target": "x86\\packing_x86.h", "type": "import", "line": 15}, {"source": "x86\\packing_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 17}, {"source": "x86\\padding_x86.cpp", "target": "x86\\padding_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\padding_x86.cpp", "target": "x86\\padding_x86.h", "type": "import", "line": 15}, {"source": "x86\\pooling_x86.cpp", "target": "x86\\pooling_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\pooling_x86.cpp", "target": "x86\\pooling_x86.h", "type": "import", "line": 15}, {"source": "x86\\prelu_x86.cpp", "target": "x86\\elu_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\prelu_x86.cpp", "target": "x86\\elu_x86.h", "type": "import", "line": 15}, {"source": "x86\\prelu_x86.cpp", "target": "x86\\prelu_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\prelu_x86.cpp", "target": "x86\\prelu_x86.h", "type": "import", "line": 15}, {"source": "x86\\prelu_x86.cpp", "target": "x86\\relu_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\prelu_x86.cpp", "target": "x86\\relu_x86.h", "type": "import", "line": 15}, {"source": "x86\\prelu_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 23}, {"source": "x86\\quantize_x86.cpp", "target": "x86\\quantize_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\quantize_x86.cpp", "target": "x86\\quantize_x86.h", "type": "import", "line": 15}, {"source": "x86\\quantize_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 24}, {"source": "x86\\relu_x86.cpp", "target": "x86\\elu_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\relu_x86.cpp", "target": "x86\\elu_x86.h", "type": "import", "line": 15}, {"source": "x86\\relu_x86.cpp", "target": "x86\\relu_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\relu_x86.cpp", "target": "x86\\relu_x86.h", "type": "import", "line": 15}, {"source": "x86\\requantize_x86.cpp", "target": "x86\\quantize_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\requantize_x86.cpp", "target": "x86\\quantize_x86.h", "type": "import", "line": 15}, {"source": "x86\\requantize_x86.cpp", "target": "x86\\requantize_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\requantize_x86.cpp", "target": "x86\\requantize_x86.h", "type": "import", "line": 15}, {"source": "x86\\requantize_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 24}, {"source": "x86\\requantize_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 25}, {"source": "x86\\reshape_x86.cpp", "target": "x86\\reshape_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\reshape_x86.cpp", "target": "x86\\reshape_x86.h", "type": "import", "line": 15}, {"source": "x86\\reshape_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 24}, {"source": "x86\\rmsnorm_x86.cpp", "target": "x86\\rmsnorm_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\rmsnorm_x86.cpp", "target": "x86\\rmsnorm_x86.h", "type": "import", "line": 15}, {"source": "x86\\rmsnorm_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 24}, {"source": "x86\\roialign_x86.cpp", "target": "x86\\roialign_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\roialign_x86.cpp", "target": "x86\\roialign_x86.h", "type": "import", "line": 15}, {"source": "x86\\scale_x86.cpp", "target": "x86\\scale_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\scale_x86.cpp", "target": "x86\\scale_x86.h", "type": "import", "line": 15}, {"source": "x86\\scale_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 23}, {"source": "x86\\selu_x86.cpp", "target": "x86\\elu_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\selu_x86.cpp", "target": "x86\\elu_x86.h", "type": "import", "line": 15}, {"source": "x86\\selu_x86.cpp", "target": "x86\\selu_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\selu_x86.cpp", "target": "x86\\selu_x86.h", "type": "import", "line": 15}, {"source": "x86\\shufflechannel_x86.cpp", "target": "x86\\shufflechannel_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\shufflechannel_x86.cpp", "target": "x86\\shufflechannel_x86.h", "type": "import", "line": 15}, {"source": "x86\\sigmoid_x86.cpp", "target": "x86\\sigmoid_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\sigmoid_x86.cpp", "target": "x86\\sigmoid_x86.h", "type": "import", "line": 15}, {"source": "x86\\slice_x86.cpp", "target": "x86\\slice_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\slice_x86.cpp", "target": "x86\\slice_x86.h", "type": "import", "line": 15}, {"source": "x86\\softmax_x86.cpp", "target": "x86\\softmax_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\softmax_x86.cpp", "target": "x86\\softmax_x86.h", "type": "import", "line": 15}, {"source": "x86\\softmax_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 31}, {"source": "x86\\swish_x86.cpp", "target": "x86\\swish_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\swish_x86.cpp", "target": "x86\\swish_x86.h", "type": "import", "line": 15}, {"source": "x86\\tanh_x86.cpp", "target": "x86\\tanh_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\tanh_x86.cpp", "target": "x86\\tanh_x86.h", "type": "import", "line": 15}, {"source": "x86\\tanh_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 17}, {"source": "x86\\unaryop_x86.cpp", "target": "x86\\unaryop_x86.cpp", "type": "import", "line": 15}, {"source": "x86\\unaryop_x86.cpp", "target": "x86\\unaryop_x86.h", "type": "import", "line": 15}, {"source": "x86\\unaryop_x86.cpp", "target": "x86\\x86_usability.h", "type": "import", "line": 34}, {"source": "x86\\unaryop_x86.cpp", "target": "x86\\x86_activation.h", "type": "import", "line": 35}, {"source": "x86\\yolov3detectionoutput_x86.cpp", "target": "x86\\yolov3detectionoutput_x86.cpp", "type": "import", "line": 18}, {"source": "x86\\yolov3detectionoutput_x86.cpp", "target": "x86\\yolov3detectionoutput_x86.h", "type": "import", "line": 18}, {"source": "x86\\rvv\\batchnorm_riscv.cpp", "target": "x86\\rvv\\batchnorm_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\batchnorm_riscv.cpp", "target": "x86\\rvv\\batchnorm_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\bias_riscv.cpp", "target": "x86\\rvv\\bias_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\bias_riscv.cpp", "target": "x86\\rvv\\bias_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\binaryop_riscv.cpp", "target": "x86\\rvv\\binaryop_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\binaryop_riscv.cpp", "target": "x86\\rvv\\binaryop_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\bnll_riscv.cpp", "target": "x86\\rvv\\bnll_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\bnll_riscv.cpp", "target": "x86\\rvv\\bnll_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\cast_riscv.cpp", "target": "x86\\rvv\\cast_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\cast_riscv.cpp", "target": "x86\\rvv\\cast_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\clip_riscv.cpp", "target": "x86\\rvv\\clip_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\clip_riscv.cpp", "target": "x86\\rvv\\clip_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\concat_riscv.cpp", "target": "x86\\rvv\\concat_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\concat_riscv.cpp", "target": "x86\\rvv\\concat_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\convolution1d_riscv.cpp", "target": "x86\\rvv\\convolution1d_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\convolution1d_riscv.cpp", "target": "x86\\rvv\\convolution1d_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "target": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "target": "x86\\rvv\\convolutiondepthwise_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\convolution_riscv.cpp", "target": "x86\\rvv\\convolution_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\convolution_riscv.cpp", "target": "x86\\rvv\\convolution_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\crop_riscv.cpp", "target": "x86\\rvv\\crop_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\crop_riscv.cpp", "target": "x86\\rvv\\crop_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "target": "x86\\rvv\\convolutiondepthwise_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "target": "x86\\rvv\\convolutiondepthwise_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\deconvolutiondepthwise_riscv.cpp", "target": "x86\\rvv\\deconvolutiondepthwise_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\deconvolution_riscv.cpp", "target": "x86\\rvv\\convolution_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\deconvolution_riscv.cpp", "target": "x86\\rvv\\convolution_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\deconvolution_riscv.cpp", "target": "x86\\rvv\\deconvolution_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\deconvolution_riscv.cpp", "target": "x86\\rvv\\deconvolution_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\deformableconv2d_riscv.cpp", "target": "x86\\rvv\\deformableconv2d_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\deformableconv2d_riscv.cpp", "target": "x86\\rvv\\deformableconv2d_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\dequantize_riscv.cpp", "target": "x86\\rvv\\dequantize_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\dequantize_riscv.cpp", "target": "x86\\rvv\\dequantize_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\dequantize_riscv.cpp", "target": "x86\\rvv\\quantize_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\dequantize_riscv.cpp", "target": "x86\\rvv\\quantize_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\dropout_riscv.cpp", "target": "x86\\rvv\\dropout_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\dropout_riscv.cpp", "target": "x86\\rvv\\dropout_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\eltwise_riscv.cpp", "target": "x86\\rvv\\eltwise_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\eltwise_riscv.cpp", "target": "x86\\rvv\\eltwise_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\elu_riscv.cpp", "target": "x86\\rvv\\elu_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\elu_riscv.cpp", "target": "x86\\rvv\\elu_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\flatten_riscv.cpp", "target": "x86\\rvv\\flatten_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\flatten_riscv.cpp", "target": "x86\\rvv\\flatten_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\gelu_riscv.cpp", "target": "x86\\rvv\\elu_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\gelu_riscv.cpp", "target": "x86\\rvv\\elu_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\gelu_riscv.cpp", "target": "x86\\rvv\\gelu_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\gelu_riscv.cpp", "target": "x86\\rvv\\gelu_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\gemm_riscv.cpp", "target": "x86\\rvv\\gemm_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\gemm_riscv.cpp", "target": "x86\\rvv\\gemm_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\gridsample_riscv.cpp", "target": "x86\\rvv\\gridsample_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\gridsample_riscv.cpp", "target": "x86\\rvv\\gridsample_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\groupnorm_riscv.cpp", "target": "x86\\rvv\\groupnorm_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\groupnorm_riscv.cpp", "target": "x86\\rvv\\groupnorm_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\hardsigmoid_riscv.cpp", "target": "x86\\rvv\\hardsigmoid_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\hardsigmoid_riscv.cpp", "target": "x86\\rvv\\hardsigmoid_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\hardsigmoid_riscv.cpp", "target": "x86\\rvv\\sigmoid_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\hardsigmoid_riscv.cpp", "target": "x86\\rvv\\sigmoid_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\hardswish_riscv.cpp", "target": "x86\\rvv\\hardswish_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\hardswish_riscv.cpp", "target": "x86\\rvv\\hardswish_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\hardswish_riscv.cpp", "target": "x86\\rvv\\swish_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\hardswish_riscv.cpp", "target": "x86\\rvv\\swish_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\innerproduct_riscv.cpp", "target": "x86\\rvv\\innerproduct_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\innerproduct_riscv.cpp", "target": "x86\\rvv\\innerproduct_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\interp_riscv.cpp", "target": "x86\\rvv\\interp_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\interp_riscv.cpp", "target": "x86\\rvv\\interp_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\layernorm_riscv.cpp", "target": "x86\\rvv\\layernorm_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\layernorm_riscv.cpp", "target": "x86\\rvv\\layernorm_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\lrn_riscv.cpp", "target": "x86\\rvv\\lrn_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\lrn_riscv.cpp", "target": "x86\\rvv\\lrn_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\lstm_riscv.cpp", "target": "x86\\rvv\\lstm_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\lstm_riscv.cpp", "target": "x86\\rvv\\lstm_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\matmul_riscv.cpp", "target": "x86\\rvv\\matmul_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\matmul_riscv.cpp", "target": "x86\\rvv\\matmul_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\mish_riscv.cpp", "target": "x86\\rvv\\mish_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\mish_riscv.cpp", "target": "x86\\rvv\\mish_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\multiheadattention_riscv.cpp", "target": "x86\\rvv\\multiheadattention_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\multiheadattention_riscv.cpp", "target": "x86\\rvv\\multiheadattention_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\packing_riscv.cpp", "target": "x86\\rvv\\packing_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\packing_riscv.cpp", "target": "x86\\rvv\\packing_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\padding_riscv.cpp", "target": "x86\\rvv\\padding_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\padding_riscv.cpp", "target": "x86\\rvv\\padding_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\pooling_riscv.cpp", "target": "x86\\rvv\\pooling_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\pooling_riscv.cpp", "target": "x86\\rvv\\pooling_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\prelu_riscv.cpp", "target": "x86\\rvv\\elu_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\prelu_riscv.cpp", "target": "x86\\rvv\\elu_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\prelu_riscv.cpp", "target": "x86\\rvv\\prelu_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\prelu_riscv.cpp", "target": "x86\\rvv\\prelu_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\prelu_riscv.cpp", "target": "x86\\rvv\\relu_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\prelu_riscv.cpp", "target": "x86\\rvv\\relu_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\quantize_riscv.cpp", "target": "x86\\rvv\\quantize_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\quantize_riscv.cpp", "target": "x86\\rvv\\quantize_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\relu_riscv.cpp", "target": "x86\\rvv\\elu_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\relu_riscv.cpp", "target": "x86\\rvv\\elu_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\relu_riscv.cpp", "target": "x86\\rvv\\relu_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\relu_riscv.cpp", "target": "x86\\rvv\\relu_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\requantize_riscv.cpp", "target": "x86\\rvv\\quantize_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\requantize_riscv.cpp", "target": "x86\\rvv\\quantize_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\requantize_riscv.cpp", "target": "x86\\rvv\\requantize_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\requantize_riscv.cpp", "target": "x86\\rvv\\requantize_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\reshape_riscv.cpp", "target": "x86\\rvv\\reshape_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\reshape_riscv.cpp", "target": "x86\\rvv\\reshape_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\rmsnorm_riscv.cpp", "target": "x86\\rvv\\rmsnorm_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\rmsnorm_riscv.cpp", "target": "x86\\rvv\\rmsnorm_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\roialign_riscv.cpp", "target": "x86\\rvv\\roialign_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\roialign_riscv.cpp", "target": "x86\\rvv\\roialign_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\scale_riscv.cpp", "target": "x86\\rvv\\scale_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\scale_riscv.cpp", "target": "x86\\rvv\\scale_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\selu_riscv.cpp", "target": "x86\\rvv\\elu_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\selu_riscv.cpp", "target": "x86\\rvv\\elu_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\selu_riscv.cpp", "target": "x86\\rvv\\selu_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\selu_riscv.cpp", "target": "x86\\rvv\\selu_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\shufflechannel_riscv.cpp", "target": "x86\\rvv\\shufflechannel_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\shufflechannel_riscv.cpp", "target": "x86\\rvv\\shufflechannel_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\sigmoid_riscv.cpp", "target": "x86\\rvv\\sigmoid_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\sigmoid_riscv.cpp", "target": "x86\\rvv\\sigmoid_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\slice_riscv.cpp", "target": "x86\\rvv\\slice_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\slice_riscv.cpp", "target": "x86\\rvv\\slice_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\softmax_riscv.cpp", "target": "x86\\rvv\\softmax_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\softmax_riscv.cpp", "target": "x86\\rvv\\softmax_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\swish_riscv.cpp", "target": "x86\\rvv\\swish_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\swish_riscv.cpp", "target": "x86\\rvv\\swish_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\tanh_riscv.cpp", "target": "x86\\rvv\\tanh_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\tanh_riscv.cpp", "target": "x86\\rvv\\tanh_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\unaryop_riscv.cpp", "target": "x86\\rvv\\unaryop_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\unaryop_riscv.cpp", "target": "x86\\rvv\\unaryop_riscv.h", "type": "import", "line": 15}, {"source": "x86\\rvv\\yolov3detectionoutput_riscv.cpp", "target": "x86\\rvv\\yolov3detectionoutput_riscv.cpp", "type": "import", "line": 15}, {"source": "x86\\rvv\\yolov3detectionoutput_riscv.cpp", "target": "x86\\rvv\\yolov3detectionoutput_riscv.h", "type": "import", "line": 15}], "exports": {"x86\\avx512_mathfun.h": [], "x86\\avx_mathfun.h": [], "x86\\batchnorm_x86.cpp": [], "x86\\batchnorm_x86.h": [], "x86\\bias_x86.cpp": [], "x86\\bias_x86.h": [], "x86\\binaryop_x86.cpp": [], "x86\\binaryop_x86.h": [], "x86\\bnll_x86.cpp": [], "x86\\bnll_x86.h": [], "x86\\cast_bf16.h": [{"type": "function", "name": "cast_fp32_to_bf16_sse", "line": 25}, {"type": "function", "name": "cast_bf16_to_fp32_sse", "line": 97}], "x86\\cast_fp16.h": [{"type": "function", "name": "cast_fp32_to_fp16_sse", "line": 20}, {"type": "function", "name": "cast_fp16_to_fp32_sse", "line": 80}], "x86\\cast_x86.cpp": [], "x86\\cast_x86.h": [], "x86\\cast_x86_avx2.cpp": [], "x86\\cast_x86_avx512bf16.cpp": [], "x86\\cast_x86_f16c.cpp": [], "x86\\clip_x86.cpp": [], "x86\\clip_x86.h": [], "x86\\concat_x86.cpp": [], "x86\\concat_x86.h": [], "x86\\convolution1d_packed.h": [{"type": "function", "name": "convolution1d_transform_kernel_packed", "line": 15}, {"type": "function", "name": "convolution1d_packed", "line": 1070}], "x86\\convolution1d_x86.cpp": [], "x86\\convolution1d_x86.h": [], "x86\\convolutiondepthwise_3x3.h": [{"type": "function", "name": "convdw3x3s1_sse", "line": 15}, {"type": "function", "name": "convdw3x3s2_sse", "line": 132}], "x86\\convolutiondepthwise_3x3_int8.h": [{"type": "function", "name": "convdw3x3s1_int8_sse", "line": 15}, {"type": "function", "name": "convdw3x3s2_int8_sse", "line": 78}, {"type": "function", "name": "convdw3x3s1_int8_dequant_sse", "line": 143}, {"type": "function", "name": "convdw3x3s2_int8_dequant_sse", "line": 208}, {"type": "function", "name": "convdw3x3s1_int8_requant_sse", "line": 276}, {"type": "function", "name": "convdw3x3s2_int8_requant_sse", "line": 340}], "x86\\convolutiondepthwise_3x3_pack16.h": [{"type": "function", "name": "convdw3x3s1_pack16_avx512", "line": 15}, {"type": "function", "name": "convdw3x3s2_pack16_avx512", "line": 510}], "x86\\convolutiondepthwise_3x3_pack4.h": [{"type": "function", "name": "convdw3x3s1_pack4_sse", "line": 15}, {"type": "function", "name": "convdw3x3s2_pack4_sse", "line": 363}], "x86\\convolutiondepthwise_3x3_pack8.h": [{"type": "function", "name": "convdw3x3s1_pack8_avx", "line": 15}, {"type": "function", "name": "convdw3x3s2_pack8_avx", "line": 572}], "x86\\convolutiondepthwise_5x5_pack16.h": [{"type": "function", "name": "convdw5x5s1_pack16_avx512", "line": 15}, {"type": "function", "name": "convdw5x5s2_pack16_avx512", "line": 165}], "x86\\convolutiondepthwise_5x5_pack4.h": [{"type": "function", "name": "convdw5x5s1_pack4_sse", "line": 15}, {"type": "function", "name": "convdw5x5s2_pack4_sse", "line": 333}], "x86\\convolutiondepthwise_5x5_pack8.h": [{"type": "function", "name": "convdw5x5s1_pack8_avx", "line": 15}, {"type": "function", "name": "convdw5x5s2_pack8_avx", "line": 165}], "x86\\convolutiondepthwise_x86.cpp": [], "x86\\convolutiondepthwise_x86.h": [], "x86\\convolution_1x1.h": [{"type": "function", "name": "conv1x1s1_sse", "line": 15}, {"type": "function", "name": "conv1x1s2_sse", "line": 106}], "x86\\convolution_2x2_pack8.h": [{"type": "function", "name": "conv2x2s1_pack8_avx", "line": 15}], "x86\\convolution_3x3.h": [{"type": "function", "name": "conv3x3s1_sse", "line": 15}, {"type": "function", "name": "conv3x3s2_sse", "line": 139}], "x86\\convolution_3x3_int8.h": [{"type": "function", "name": "conv3x3s1_int8_sse", "line": 15}, {"type": "function", "name": "conv3x3s2_int8_sse", "line": 81}], "x86\\convolution_3x3_pack16to1.h": [{"type": "function", "name": "conv3x3s1_pack16to1_avx512", "line": 15}], "x86\\convolution_3x3_pack1to4.h": [{"type": "function", "name": "conv3x3s1_pack1to4_sse", "line": 15}, {"type": "function", "name": "conv3x3s2_pack1to4_sse", "line": 545}], "x86\\convolution_3x3_pack1to8.h": [{"type": "function", "name": "conv3x3s1_pack1to8_avx", "line": 15}, {"type": "function", "name": "conv3x3s2_pack1to8_avx", "line": 545}], "x86\\convolution_3x3_pack8.h": [{"type": "function", "name": "conv3x3s1_pack8_avx", "line": 15}], "x86\\convolution_3x3_pack8to1.h": [{"type": "function", "name": "conv3x3s1_pack8to1_avx", "line": 15}], "x86\\convolution_3x3_winograd.h": [], "x86\\convolution_3x3_winograd_int8.h": [], "x86\\convolution_5x5.h": [{"type": "function", "name": "conv5x5s1_sse", "line": 15}], "x86\\convolution_im2col_gemm.h": [], "x86\\convolution_im2col_gemm_int8.h": [], "x86\\convolution_packed.h": [{"type": "function", "name": "convolution_transform_kernel_packed", "line": 15}, {"type": "function", "name": "convolution_packed", "line": 1073}], "x86\\convolution_packed_int8.h": [{"type": "function", "name": "convolution_transform_kernel_packed_int8", "line": 32}, {"type": "function", "name": "convolution_packed_int8", "line": 876}], "x86\\convolution_x86.cpp": [], "x86\\convolution_x86.h": [], "x86\\convolution_x86_avx2.cpp": [], "x86\\convolution_x86_avx512vnni.cpp": [], "x86\\convolution_x86_avxvnni.cpp": [], "x86\\convolution_x86_avxvnniint8.cpp": [], "x86\\convolution_x86_xop.cpp": [], "x86\\crop_x86.cpp": [], "x86\\crop_x86.h": [], "x86\\deconvolutiondepthwise_x86.cpp": [], "x86\\deconvolutiondepthwise_x86.h": [], "x86\\deconvolution_pack16.h": [{"type": "function", "name": "deconvolution_pack16_avx512", "line": 15}], "x86\\deconvolution_pack16to1.h": [{"type": "function", "name": "deconvolution_pack16to1_avx512", "line": 15}], "x86\\deconvolution_pack16to4.h": [{"type": "function", "name": "deconvolution_pack16to4_avx512", "line": 15}], "x86\\deconvolution_pack16to8.h": [{"type": "function", "name": "deconvolution_pack16to8_avx512", "line": 15}], "x86\\deconvolution_pack1to16.h": [{"type": "function", "name": "deconvolution_pack1to16_avx512", "line": 15}], "x86\\deconvolution_pack1to4.h": [{"type": "function", "name": "deconvolution_pack1to4_sse", "line": 15}], "x86\\deconvolution_pack1to8.h": [{"type": "function", "name": "deconvolution_pack1to8_avx", "line": 15}], "x86\\deconvolution_pack4.h": [{"type": "function", "name": "deconvolution_pack4_sse", "line": 15}], "x86\\deconvolution_pack4to1.h": [{"type": "function", "name": "deconvolution_pack4to1_sse", "line": 15}], "x86\\deconvolution_pack4to16.h": [{"type": "function", "name": "deconvolution_pack4to16_avx512", "line": 15}], "x86\\deconvolution_pack4to8.h": [{"type": "function", "name": "deconvolution_pack4to8_avx", "line": 15}], "x86\\deconvolution_pack8.h": [{"type": "function", "name": "deconvolution_pack8_avx", "line": 15}], "x86\\deconvolution_pack8to1.h": [{"type": "function", "name": "deconvolution_pack8to1_avx", "line": 15}], "x86\\deconvolution_pack8to16.h": [{"type": "function", "name": "deconvolution_pack8to16_avx512", "line": 15}], "x86\\deconvolution_pack8to4.h": [{"type": "function", "name": "deconvolution_pack8to4_avx", "line": 15}], "x86\\deconvolution_x86.cpp": [], "x86\\deconvolution_x86.h": [], "x86\\deformableconv2d_pack16.h": [{"type": "function", "name": "deformableconv2d_pack16_avx512", "line": 15}], "x86\\deformableconv2d_pack16to1.h": [{"type": "function", "name": "deformableconv2d_pack16to1_avx512", "line": 15}], "x86\\deformableconv2d_pack16to4.h": [{"type": "function", "name": "deformableconv2d_pack16to4_avx512", "line": 15}], "x86\\deformableconv2d_pack16to8.h": [{"type": "function", "name": "deformableconv2d_pack16to8_avx512", "line": 15}], "x86\\deformableconv2d_pack1to16.h": [{"type": "function", "name": "deformableconv2d_pack1to16_avx512", "line": 15}], "x86\\deformableconv2d_pack1to4.h": [{"type": "function", "name": "deformableconv2d_pack1to4_sse", "line": 15}], "x86\\deformableconv2d_pack1to8.h": [{"type": "function", "name": "deformableconv2d_pack1to8_avx", "line": 15}], "x86\\deformableconv2d_pack4.h": [{"type": "function", "name": "deformableconv2d_pack4_sse", "line": 15}], "x86\\deformableconv2d_pack4to1.h": [{"type": "function", "name": "deformableconv2d_pack4to1_sse", "line": 15}], "x86\\deformableconv2d_pack4to16.h": [{"type": "function", "name": "deformableconv2d_pack4to16_avx512", "line": 15}], "x86\\deformableconv2d_pack4to8.h": [{"type": "function", "name": "deformableconv2d_pack4to8_avx", "line": 15}], "x86\\deformableconv2d_pack8.h": [{"type": "function", "name": "deformableconv2d_pack8_avx", "line": 15}], "x86\\deformableconv2d_pack8to1.h": [{"type": "function", "name": "deformableconv2d_pack8to1_avx", "line": 15}], "x86\\deformableconv2d_pack8to16.h": [{"type": "function", "name": "deformableconv2d_pack8to16_avx512", "line": 15}], "x86\\deformableconv2d_pack8to4.h": [{"type": "function", "name": "deformableconv2d_pack8to4_avx", "line": 15}], "x86\\deformableconv2d_x86.cpp": [], "x86\\deformableconv2d_x86.h": [], "x86\\dequantize_x86.cpp": [], "x86\\dequantize_x86.h": [], "x86\\dropout_x86.cpp": [], "x86\\dropout_x86.h": [], "x86\\eltwise_x86.cpp": [], "x86\\eltwise_x86.h": [], "x86\\elu_x86.cpp": [], "x86\\elu_x86.h": [], "x86\\flatten_x86.cpp": [], "x86\\flatten_x86.h": [], "x86\\gelu_x86.cpp": [], "x86\\gelu_x86.h": [], "x86\\gemm_int8.h": [{"type": "function", "name": "pack_A_tile_int8", "line": 68}, {"type": "function", "name": "transpose_pack_A_tile_int8", "line": 416}, {"type": "function", "name": "pack_B_tile_int8", "line": 756}, {"type": "function", "name": "transpose_pack_B_tile_int8", "line": 1118}, {"type": "function", "name": "compute_A_tile_fp32_int8_scales", "line": 1414}, {"type": "function", "name": "pack_A_tile_fp32_to_int8", "line": 1567}, {"type": "function", "name": "transpose_compute_A_tile_fp32_int8_scales", "line": 2582}, {"type": "function", "name": "transpose_pack_A_tile_fp32_to_int8", "line": 3345}, {"type": "function", "name": "compute_B_fp32_int8_scale", "line": 4922}, {"type": "function", "name": "pack_B_tile_fp32_to_int8", "line": 4987}, {"type": "function", "name": "transpose_pack_B_tile_fp32_to_int8", "line": 6060}, {"type": "function", "name": "unpack_output_tile_int32_to_fp32", "line": 7472}, {"type": "function", "name": "gemm_transB_packed_tile_int8", "line": 12483}, {"type": "function", "name": "get_optimal_tile_mnk_int8", "line": 15503}], "x86\\gemm_x86.cpp": [], "x86\\gemm_x86.h": [], "x86\\gemm_x86_avx2.cpp": [], "x86\\gemm_x86_avx512vnni.cpp": [], "x86\\gemm_x86_avxvnni.cpp": [], "x86\\gemm_x86_avxvnniint8.cpp": [], "x86\\gemm_x86_xop.cpp": [], "x86\\generate_riscv_operators.py": [{"type": "function", "name": "generate_header", "line": 57}, {"type": "function", "name": "generate_simple_activation_cpp", "line": 93}, {"type": "function", "name": "generate_files", "line": 163}], "x86\\gridsample_bicubic_apply_interpolation.h": [{"type": "function", "name": "cubic_interp1d", "line": 223}, {"type": "function", "name": "gridsample_2d_bicubic_apply_interpolation_p1", "line": 238}], "x86\\gridsample_bicubic_compute_blob.h": [], "x86\\gridsample_bilinear_apply_interpolation.h": [{"type": "function", "name": "gridsample_2d_bilinear_apply_interpolation_p1", "line": 289}, {"type": "function", "name": "gridsample_3d_bilinear_apply_interpolation_p1", "line": 325}], "x86\\gridsample_bilinear_compute_blob.h": [], "x86\\gridsample_compute_blob.h": [], "x86\\gridsample_nearest_apply_interpolation.h": [{"type": "function", "name": "gridsample_nearest_apply_interpolation_p1", "line": 102}], "x86\\gridsample_nearest_compute_blob.h": [], "x86\\gridsample_x86.cpp": [], "x86\\gridsample_x86.h": [], "x86\\groupnorm_x86.cpp": [], "x86\\groupnorm_x86.h": [], "x86\\hardsigmoid_x86.cpp": [], "x86\\hardsigmoid_x86.h": [], "x86\\hardswish_x86.cpp": [], "x86\\hardswish_x86.h": [], "x86\\innerproduct_fp.h": [], "x86\\innerproduct_gemm_fp.h": [], "x86\\innerproduct_x86.cpp": [], "x86\\innerproduct_x86.h": [], "x86\\innerproduct_x86_f16c.cpp": [], "x86\\interp_bicubic.h": [{"type": "function", "name": "interpolate_cubic", "line": 15}, {"type": "function", "name": "cubic_coeffs", "line": 30}, {"type": "function", "name": "resize_bicubic_image", "line": 88}], "x86\\interp_bicubic_pack16.h": [{"type": "function", "name": "resize_bicubic_image_pack16", "line": 15}], "x86\\interp_bicubic_pack4.h": [{"type": "function", "name": "resize_bicubic_image_pack4", "line": 15}], "x86\\interp_bicubic_pack8.h": [{"type": "function", "name": "resize_bicubic_image_pack8", "line": 15}], "x86\\interp_bilinear.h": [{"type": "function", "name": "linear_coeffs", "line": 15}, {"type": "function", "name": "resize_bilinear_image", "line": 52}], "x86\\interp_bilinear_pack16.h": [{"type": "function", "name": "resize_bilinear_image_pack16", "line": 15}], "x86\\interp_bilinear_pack4.h": [{"type": "function", "name": "resize_bilinear_image_pack4", "line": 15}], "x86\\interp_bilinear_pack8.h": [{"type": "function", "name": "resize_bilinear_image_pack8", "line": 15}], "x86\\interp_x86.cpp": [], "x86\\interp_x86.h": [], "x86\\layernorm_x86.cpp": [], "x86\\layernorm_x86.h": [], "x86\\lrn_x86.cpp": [], "x86\\lrn_x86.h": [], "x86\\lstm_int8.h": [{"type": "function", "name": "lstm_transform_weight_int8", "line": 36}, {"type": "function", "name": "lstm_dynamic_quantize_get_absmax", "line": 1570}, {"type": "function", "name": "lstm_dynamic_quantize_scale2int8", "line": 1614}, {"type": "function", "name": "lstm_int8", "line": 1698}], "x86\\lstm_x86.cpp": [], "x86\\lstm_x86.h": [], "x86\\lstm_x86_avx2.cpp": [], "x86\\lstm_x86_avx512vnni.cpp": [], "x86\\lstm_x86_avxvnni.cpp": [], "x86\\lstm_x86_xop.cpp": [], "x86\\matmul_x86.cpp": [], "x86\\matmul_x86.h": [], "x86\\mish_x86.cpp": [], "x86\\mish_x86.h": [], "x86\\multiheadattention_x86.cpp": [], "x86\\multiheadattention_x86.h": [], "x86\\packing_x86.cpp": [], "x86\\packing_x86.h": [], "x86\\padding_pack16.h": [{"type": "function", "name": "padding_constant_pack16_avx512", "line": 15}, {"type": "function", "name": "padding_replicate_pack16_avx512", "line": 56}, {"type": "function", "name": "padding_reflect_pack16_avx512", "line": 132}], "x86\\padding_pack4.h": [{"type": "function", "name": "padding_constant_pack4_sse", "line": 15}, {"type": "function", "name": "padding_replicate_pack4_sse", "line": 56}, {"type": "function", "name": "padding_reflect_pack4_sse", "line": 132}], "x86\\padding_pack8.h": [{"type": "function", "name": "padding_constant_pack8_avx", "line": 15}, {"type": "function", "name": "padding_replicate_pack8_avx", "line": 56}, {"type": "function", "name": "padding_reflect_pack8_avx", "line": 132}], "x86\\padding_pack8_int8.h": [{"type": "function", "name": "padding_constant_pack8_int8_sse", "line": 15}, {"type": "function", "name": "padding_replicate_pack8_int8_sse", "line": 54}, {"type": "function", "name": "padding_reflect_pack8_int8_sse", "line": 112}], "x86\\padding_x86.cpp": [], "x86\\padding_x86.h": [], "x86\\pooling_2x2.h": [{"type": "function", "name": "pooling2x2s2_max_avx", "line": 18}], "x86\\pooling_2x2_pack16.h": [{"type": "function", "name": "pooling2x2s2_max_pack16_avx512", "line": 15}], "x86\\pooling_2x2_pack4.h": [{"type": "function", "name": "pooling2x2s2_max_pack4_sse", "line": 16}], "x86\\pooling_2x2_pack8.h": [{"type": "function", "name": "pooling2x2s2_max_pack8_avx", "line": 16}], "x86\\pooling_3x3_pack16.h": [{"type": "function", "name": "pooling3x3s2_max_pack16_avx512", "line": 15}], "x86\\pooling_3x3_pack4.h": [{"type": "function", "name": "pooling3x3s2_max_pack4_sse", "line": 16}], "x86\\pooling_3x3_pack8.h": [{"type": "function", "name": "pooling3x3s2_max_pack8_avx", "line": 16}], "x86\\pooling_x86.cpp": [], "x86\\pooling_x86.h": [], "x86\\prelu_x86.cpp": [], "x86\\prelu_x86.h": [], "x86\\quantize_x86.cpp": [], "x86\\quantize_x86.h": [], "x86\\relu_x86.cpp": [], "x86\\relu_x86.h": [], "x86\\requantize_x86.cpp": [], "x86\\requantize_x86.h": [], "x86\\reshape_x86.cpp": [], "x86\\reshape_x86.h": [], "x86\\rmsnorm_x86.cpp": [], "x86\\rmsnorm_x86.h": [], "x86\\roialign_x86.cpp": [], "x86\\roialign_x86.h": [], "x86\\scale_x86.cpp": [], "x86\\scale_x86.h": [], "x86\\selu_x86.cpp": [], "x86\\selu_x86.h": [], "x86\\shufflechannel_x86.cpp": [], "x86\\shufflechannel_x86.h": [], "x86\\sigmoid_x86.cpp": [], "x86\\sigmoid_x86.h": [], "x86\\slice_x86.cpp": [], "x86\\slice_x86.h": [], "x86\\softmax_x86.cpp": [], "x86\\softmax_x86.h": [], "x86\\sse_mathfun.h": [], "x86\\swish_x86.cpp": [], "x86\\swish_x86.h": [], "x86\\tanh_x86.cpp": [], "x86\\tanh_x86.h": [], "x86\\unaryop_x86.cpp": [], "x86\\unaryop_x86.h": [], "x86\\x86_activation.h": [], "x86\\x86_usability.h": [], "x86\\yolov3detectionoutput_x86.cpp": [], "x86\\yolov3detectionoutput_x86.h": [], "x86\\rvv\\batchnorm_riscv.cpp": [], "x86\\rvv\\batchnorm_riscv.h": [], "x86\\rvv\\batch_create_operators.py": [{"type": "function", "name": "create_header", "line": 61}, {"type": "function", "name": "create_implementation", "line": 126}, {"type": "function", "name": "main", "line": 225}], "x86\\rvv\\bias_riscv.cpp": [], "x86\\rvv\\bias_riscv.h": [], "x86\\rvv\\binaryop_riscv.cpp": [], "x86\\rvv\\binaryop_riscv.h": [], "x86\\rvv\\bnll_riscv.cpp": [], "x86\\rvv\\bnll_riscv.h": [], "x86\\rvv\\cast_riscv.cpp": [], "x86\\rvv\\cast_riscv.h": [], "x86\\rvv\\clip_riscv.cpp": [], "x86\\rvv\\clip_riscv.h": [], "x86\\rvv\\concat_riscv.cpp": [], "x86\\rvv\\concat_riscv.h": [], "x86\\rvv\\convolution1d_riscv.cpp": [], "x86\\rvv\\convolution1d_riscv.h": [], "x86\\rvv\\convolutiondepthwise_riscv.cpp": [], "x86\\rvv\\convolutiondepthwise_riscv.h": [], "x86\\rvv\\convolution_riscv.cpp": [], "x86\\rvv\\convolution_riscv.h": [], "x86\\rvv\\crop_riscv.cpp": [], "x86\\rvv\\crop_riscv.h": [], "x86\\rvv\\deconvolutiondepthwise_riscv.cpp": [], "x86\\rvv\\deconvolutiondepthwise_riscv.h": [], "x86\\rvv\\deconvolution_riscv.cpp": [], "x86\\rvv\\deconvolution_riscv.h": [], "x86\\rvv\\deformableconv2d_riscv.cpp": [], "x86\\rvv\\deformableconv2d_riscv.h": [], "x86\\rvv\\dequantize_riscv.cpp": [], "x86\\rvv\\dequantize_riscv.h": [], "x86\\rvv\\dropout_riscv.cpp": [], "x86\\rvv\\dropout_riscv.h": [], "x86\\rvv\\eltwise_riscv.cpp": [], "x86\\rvv\\eltwise_riscv.h": [], "x86\\rvv\\elu_riscv.cpp": [], "x86\\rvv\\elu_riscv.h": [], "x86\\rvv\\flatten_riscv.cpp": [], "x86\\rvv\\flatten_riscv.h": [], "x86\\rvv\\gelu_riscv.cpp": [], "x86\\rvv\\gelu_riscv.h": [], "x86\\rvv\\gemm_riscv.cpp": [], "x86\\rvv\\gemm_riscv.h": [], "x86\\rvv\\gridsample_riscv.cpp": [], "x86\\rvv\\gridsample_riscv.h": [], "x86\\rvv\\groupnorm_riscv.cpp": [], "x86\\rvv\\groupnorm_riscv.h": [], "x86\\rvv\\hardsigmoid_riscv.cpp": [], "x86\\rvv\\hardsigmoid_riscv.h": [], "x86\\rvv\\hardswish_riscv.cpp": [], "x86\\rvv\\hardswish_riscv.h": [], "x86\\rvv\\innerproduct_riscv.cpp": [], "x86\\rvv\\innerproduct_riscv.h": [], "x86\\rvv\\interp_riscv.cpp": [], "x86\\rvv\\interp_riscv.h": [], "x86\\rvv\\layernorm_riscv.cpp": [], "x86\\rvv\\layernorm_riscv.h": [], "x86\\rvv\\lrn_riscv.cpp": [], "x86\\rvv\\lrn_riscv.h": [], "x86\\rvv\\lstm_riscv.cpp": [], "x86\\rvv\\lstm_riscv.h": [], "x86\\rvv\\matmul_riscv.cpp": [], "x86\\rvv\\matmul_riscv.h": [], "x86\\rvv\\mish_riscv.cpp": [], "x86\\rvv\\mish_riscv.h": [], "x86\\rvv\\multiheadattention_riscv.cpp": [], "x86\\rvv\\multiheadattention_riscv.h": [], "x86\\rvv\\packing_riscv.cpp": [], "x86\\rvv\\packing_riscv.h": [], "x86\\rvv\\padding_riscv.cpp": [], "x86\\rvv\\padding_riscv.h": [], "x86\\rvv\\pooling_riscv.cpp": [], "x86\\rvv\\pooling_riscv.h": [], "x86\\rvv\\prelu_riscv.cpp": [], "x86\\rvv\\prelu_riscv.h": [], "x86\\rvv\\quantize_riscv.cpp": [], "x86\\rvv\\quantize_riscv.h": [], "x86\\rvv\\relu_riscv.cpp": [], "x86\\rvv\\relu_riscv.h": [], "x86\\rvv\\requantize_riscv.cpp": [], "x86\\rvv\\requantize_riscv.h": [], "x86\\rvv\\reshape_riscv.cpp": [], "x86\\rvv\\reshape_riscv.h": [], "x86\\rvv\\riscv_usability.h": [], "x86\\rvv\\rmsnorm_riscv.cpp": [], "x86\\rvv\\rmsnorm_riscv.h": [], "x86\\rvv\\roialign_riscv.cpp": [], "x86\\rvv\\roialign_riscv.h": [], "x86\\rvv\\rvv_mathfun.h": [], "x86\\rvv\\scale_riscv.cpp": [], "x86\\rvv\\scale_riscv.h": [], "x86\\rvv\\selu_riscv.cpp": [], "x86\\rvv\\selu_riscv.h": [], "x86\\rvv\\shufflechannel_riscv.cpp": [], "x86\\rvv\\shufflechannel_riscv.h": [], "x86\\rvv\\sigmoid_riscv.cpp": [], "x86\\rvv\\sigmoid_riscv.h": [], "x86\\rvv\\slice_riscv.cpp": [], "x86\\rvv\\slice_riscv.h": [], "x86\\rvv\\softmax_riscv.cpp": [], "x86\\rvv\\softmax_riscv.h": [], "x86\\rvv\\swish_riscv.cpp": [], "x86\\rvv\\swish_riscv.h": [], "x86\\rvv\\tanh_riscv.cpp": [], "x86\\rvv\\tanh_riscv.h": [], "x86\\rvv\\test_riscv_operators.cpp": [{"type": "function", "name": "print_vector", "line": 176}, {"type": "function", "name": "main", "line": 184}], "x86\\rvv\\unaryop_riscv.cpp": [], "x86\\rvv\\unaryop_riscv.h": [], "x86\\rvv\\yolov3detectionoutput_riscv.cpp": [], "x86\\rvv\\yolov3detectionoutput_riscv.h": []}}