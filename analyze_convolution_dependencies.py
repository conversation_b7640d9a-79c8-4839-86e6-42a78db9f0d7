#!/usr/bin/env python3
"""
分析x86文件夹中convolution卷积算子的依赖关系
"""

import json
from collections import defaultdict, Counter

def analyze_convolution_dependencies():
    """分析convolution相关文件的依赖关系"""
    
    print("=== x86文件夹中convolution卷积算子依赖分析 ===\n")
    
    # 读取依赖关系报告
    try:
        with open('x86_dependency_report.json', 'r', encoding='utf-8') as f:
            report = json.load(f)
    except FileNotFoundError:
        print("❌ 找不到依赖关系报告文件")
        return
    
    # 1. 统计所有convolution相关文件
    print("1. 统计convolution相关文件...")
    convolution_files = []
    
    for file_info in report['files']:
        filename = file_info['name'].lower()
        if 'convolution' in filename or 'deconvolution' in filename:
            convolution_files.append(file_info)
    
    print(f"   找到 {len(convolution_files)} 个convolution相关文件")
    
    # 按类型分类
    conv_types = defaultdict(list)
    for file_info in convolution_files:
        filename = file_info['name']
        
        if 'convolution1d' in filename:
            conv_types['1D卷积'].append(file_info)
        elif 'convolutiondepthwise' in filename:
            conv_types['深度可分离卷积'].append(file_info)
        elif 'deconvolutiondepthwise' in filename:
            conv_types['深度可分离反卷积'].append(file_info)
        elif 'deconvolution' in filename:
            conv_types['反卷积'].append(file_info)
        elif 'convolution' in filename:
            conv_types['标准卷积'].append(file_info)
    
    print("\n   按类型分类:")
    for conv_type, files in conv_types.items():
        print(f"     {conv_type}: {len(files)} 个文件")
    
    # 2. 分析依赖关系
    print("\n2. 分析convolution文件的依赖关系...")
    
    # 统计被依赖的convolution文件
    convolution_dependencies = defaultdict(list)
    convolution_dependents = defaultdict(int)
    
    for dep in report['dependencies']:
        source = dep['source']
        target = dep['target']
        
        # 检查是否涉及convolution文件
        source_is_conv = any('convolution' in source.lower() or 'deconvolution' in source.lower() for _ in [1])
        target_is_conv = any('convolution' in target.lower() or 'deconvolution' in target.lower() for _ in [1])
        
        if source_is_conv or target_is_conv:
            convolution_dependencies[source].append({
                'target': target,
                'type': dep['type'],
                'line': dep.get('line', 0)
            })
            
            if target_is_conv:
                convolution_dependents[target] += 1
    
    # 3. 找出核心convolution文件
    print("\n3. 核心convolution文件（被依赖最多）:")
    
    # 从核心文件列表中筛选convolution相关的
    core_conv_files = []
    for core_file in report['summary']['core_files']:
        filename = core_file['file'].lower()
        if 'convolution' in filename or 'deconvolution' in filename:
            core_conv_files.append(core_file)
    
    if core_conv_files:
        for file_info in core_conv_files:
            print(f"     {file_info['file']}: 被 {file_info['dependents']} 个文件依赖")
    else:
        # 如果核心文件中没有convolution，从所有依赖中统计
        sorted_conv_deps = sorted(convolution_dependents.items(), key=lambda x: x[1], reverse=True)[:5]
        for file_path, count in sorted_conv_deps:
            if count > 0:
                print(f"     {file_path}: 被 {count} 个文件依赖")
    
    # 4. 分析convolution文件的具体依赖
    print("\n4. convolution文件依赖的其他文件:")
    
    # 统计convolution文件依赖的文件类型
    dependency_targets = defaultdict(int)
    
    for source, deps in convolution_dependencies.items():
        if 'convolution' in source.lower() or 'deconvolution' in source.lower():
            for dep in deps:
                target = dep['target']
                dependency_targets[target] += 1
    
    # 找出被convolution文件依赖最多的文件
    top_dependencies = sorted(dependency_targets.items(), key=lambda x: x[1], reverse=True)[:10]
    
    print("   被convolution文件依赖最多的文件:")
    for target, count in top_dependencies:
        print(f"     {target}: 被 {count} 个convolution文件依赖")
    
    # 5. 分析文件大小
    print("\n5. convolution文件大小分析:")
    
    # 按大小排序
    sorted_by_size = sorted(convolution_files, key=lambda x: x['size'], reverse=True)[:10]
    
    print("   最大的convolution文件:")
    for file_info in sorted_by_size:
        size_mb = file_info['size'] / 1024 / 1024
        print(f"     {file_info['name']}: {size_mb:.2f} MB ({file_info['size']:,} 字节)")
    
    # 6. 分析指令集优化版本
    print("\n6. 指令集优化版本分析:")
    
    instruction_sets = defaultdict(list)
    for file_info in convolution_files:
        filename = file_info['name']
        
        if 'avx512' in filename:
            instruction_sets['AVX512'].append(filename)
        elif 'avx2' in filename:
            instruction_sets['AVX2'].append(filename)
        elif 'avx' in filename:
            instruction_sets['AVX'].append(filename)
        elif 'sse' in filename:
            instruction_sets['SSE'].append(filename)
        elif 'riscv' in filename:
            instruction_sets['RISC-V'].append(filename)
        elif 'pack' in filename:
            instruction_sets['向量打包优化'].append(filename)
        elif 'int8' in filename:
            instruction_sets['INT8量化'].append(filename)
        elif 'winograd' in filename:
            instruction_sets['Winograd算法'].append(filename)
        else:
            instruction_sets['通用版本'].append(filename)
    
    for inst_set, files in instruction_sets.items():
        if files:
            print(f"     {inst_set}: {len(files)} 个文件")
    
    # 7. 生成详细统计
    print("\n7. 详细统计:")
    
    total_conv_files = len(convolution_files)
    total_conv_size = sum(f['size'] for f in convolution_files)
    total_conv_size_mb = total_conv_size / 1024 / 1024
    
    cpp_files = [f for f in convolution_files if f['extension'] == '.cpp']
    h_files = [f for f in convolution_files if f['extension'] == '.h']
    
    print(f"   总文件数: {total_conv_files}")
    print(f"   总大小: {total_conv_size_mb:.2f} MB")
    print(f"   .cpp文件: {len(cpp_files)} 个")
    print(f"   .h文件: {len(h_files)} 个")
    print(f"   平均文件大小: {total_conv_size / total_conv_files / 1024:.1f} KB")
    
    # 8. 依赖关系网络分析
    print("\n8. 依赖关系网络分析:")
    
    # 统计convolution文件之间的相互依赖
    conv_to_conv_deps = 0
    conv_to_other_deps = 0
    other_to_conv_deps = 0
    
    for dep in report['dependencies']:
        source = dep['source']
        target = dep['target']
        
        source_is_conv = 'convolution' in source.lower() or 'deconvolution' in source.lower()
        target_is_conv = 'convolution' in target.lower() or 'deconvolution' in target.lower()
        
        if source_is_conv and target_is_conv:
            conv_to_conv_deps += 1
        elif source_is_conv and not target_is_conv:
            conv_to_other_deps += 1
        elif not source_is_conv and target_is_conv:
            other_to_conv_deps += 1
    
    print(f"   convolution文件间相互依赖: {conv_to_conv_deps} 个")
    print(f"   convolution文件依赖其他文件: {conv_to_other_deps} 个")
    print(f"   其他文件依赖convolution文件: {other_to_conv_deps} 个")
    print(f"   总依赖关系: {conv_to_conv_deps + conv_to_other_deps + other_to_conv_deps} 个")
    
    # 9. 关键依赖文件
    print("\n9. convolution算子的关键依赖文件:")
    
    # 分析convolution文件最常依赖的非convolution文件
    non_conv_dependencies = defaultdict(int)
    
    for source, deps in convolution_dependencies.items():
        if 'convolution' in source.lower() or 'deconvolution' in source.lower():
            for dep in deps:
                target = dep['target']
                if not ('convolution' in target.lower() or 'deconvolution' in target.lower()):
                    non_conv_dependencies[target] += 1
    
    top_non_conv_deps = sorted(non_conv_dependencies.items(), key=lambda x: x[1], reverse=True)[:10]
    
    for target, count in top_non_conv_deps:
        print(f"     {target}: 被 {count} 个convolution文件依赖")
    
    print(f"\n=== 分析完成 ===")
    print(f"convolution算子共涉及 {total_conv_files} 个文件，")
    print(f"总代码量 {total_conv_size_mb:.1f} MB，")
    print(f"参与了 {conv_to_conv_deps + conv_to_other_deps + other_to_conv_deps} 个依赖关系。")

if __name__ == "__main__":
    analyze_convolution_dependencies()
