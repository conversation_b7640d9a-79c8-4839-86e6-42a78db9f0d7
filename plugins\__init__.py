# Requirements
from plugins.location_tools import (
    # AgentSkillsPlugin,
    LocationToolsRequirement,
)
# from openhands.runtime.plugins.jupyter import Ju<PERSON><PERSON>Plugin, JupyterRequirement
from plugins.requirement import PluginRequirement #, Plugin

__all__ = [
    # 'Plugin',
    'PluginRequirement',
    # 'AgentSkillsRequirement',
    # 'AgentSkillsPlugin',
    # 'JupyterRequirement',
    # 'JupyterPlugin',
    'LocationToolsRequirement'
]

# ALL_PLUGINS = {
#     'jupyter': <PERSON><PERSON><PERSON>Plugin,
#     'agent_skills': AgentSkillsPlugin,
# }


