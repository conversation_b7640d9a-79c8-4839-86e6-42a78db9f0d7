import logging

from tree_sitter_languages import get_language

from repo_index.codeblocks.codeblocks import (
    <PERSON><PERSON><PERSON>,
    CodeBlockType,
    ReferenceScope,
    RelationshipType,
    ValidationError,
)
from repo_index.codeblocks.parser.parser import (
    <PERSON><PERSON>ars<PERSON>,
    NodeMatch,
    commented_out_keywords,
)

child_block_types = ['ERROR', 'compound_statement']

block_delimiters = ['{']

logger = logging.getLogger(__name__)


class CppParser(CodeParser):
    def __init__(self, **kwargs):
        language = get_language('cpp')

        super().__init__(language, **kwargs)
        self.queries.extend(self._build_queries('cpp.scm'))

    @property
    def language(self):
        return 'cpp'

    def pre_process(self, codeblock: CodeBlock, node_match: NodeMatch):
        # Handle constructors - if function name matches class name, mark as constructor
        if (
            codeblock.type == CodeBlockType.FUNCTION
            and codeblock.parent
            and codeblock.parent.type == CodeBlockType.CLASS
            and codeblock.identifier == codeblock.parent.identifier
        ):
            codeblock.type = CodeBlockType.CONSTRUCTOR

    def post_process(self, codeblock: CodeBlock):
        # <PERSON><PERSON> commented out code detection
        if codeblock.type == CodeBlockType.COMMENT and self.is_outcommented_code(
            codeblock.content
        ):
            codeblock.type = CodeBlockType.COMMENTED_OUT_CODE

        # Set reference types for field declarations
        if codeblock.type == CodeBlockType.FIELD:
            for reference in codeblock.relationships:
                reference.type = RelationshipType.TYPE

        # Handle namespace and class scope references
        new_references: list = []
        for reference in codeblock.relationships:
            # Set parent class path as reference path for class members
            if reference.path and len(reference.path) > 0:
                class_block = codeblock.find_type_in_parents(CodeBlockType.CLASS)
                namespace_block = codeblock.find_type_in_parents(CodeBlockType.NAMESPACE)
                
                if class_block:
                    reference.scope = ReferenceScope.CLASS
                elif namespace_block:
                    reference.scope = ReferenceScope.MODULE

        codeblock.relationships.extend(new_references)

        # Mark empty classes/functions as commented out if they only contain comments
        if (
            codeblock.type in [CodeBlockType.CLASS, CodeBlockType.FUNCTION, CodeBlockType.NAMESPACE]
            and len(codeblock.children) == 1
            and codeblock.children[0].type == CodeBlockType.COMMENTED_OUT_CODE
        ):
            codeblock.type = CodeBlockType.COMMENTED_OUT_CODE

        # Check for duplicate function/class names
        function_names = set()
        class_names = set()
        for child in codeblock.children:
            if child.type == CodeBlockType.FUNCTION:
                if child.identifier in function_names:
                    child.validation_errors.append(
                        ValidationError(
                            error=f'Duplicate function name: {child.identifier}'
                        )
                    )
                function_names.add(child.identifier)
            if child.type == CodeBlockType.CLASS:
                if child.identifier in class_names:
                    child.validation_errors.append(
                        ValidationError(
                            error=f'Duplicate class name: {child.identifier}'
                        )
                    )
                class_names.add(child.identifier)

    def is_outcommented_code(self, comment):
        # C++ comments start with // or /* */
        comment_content = comment.strip()
        if comment_content.startswith('//'):
            comment_content = comment_content[2:].strip()
        elif comment_content.startswith('/*') and comment_content.endswith('*/'):
            comment_content = comment_content[2:-2].strip()
        
        return comment_content.startswith('...') or any(
            keyword in comment_content.lower() for keyword in commented_out_keywords
        )
