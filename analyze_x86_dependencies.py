#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析x86文件夹中代码的依赖和引用关系
"""

import os
import json
import networkx as nx
from collections import defaultdict
from pathlib import Path

def analyze_x86_dependencies():
    """分析x86文件夹的依赖关系"""
    
    folder_path = r"C:\Users\<USER>\Desktop\x86"
    
    print(f"=== 分析x86文件夹的依赖关系 ===")
    print(f"目标文件夹: {folder_path}\n")
    
    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"❌ 文件夹不存在: {folder_path}")
        return
    
    # 导入LocAgent工具
    try:
        from repo_index.codeblocks import get_parser_by_path, supports_codeblocks
        from dependency_graph.build_graph import analyze_file, find_imports, build_graph
        from dependency_graph.traverse_graph import RepoEntitySearcher, RepoDependencySearcher
        print("✅ LocAgent依赖分析工具导入成功")
    except Exception as e:
        print(f"❌ 导入工具失败: {e}")
        return
    
    # 1. 扫描所有代码文件
    print("\n1. 扫描代码文件...")
    code_files = []
    file_dependencies = {}
    file_exports = {}
    
    # 支持的文件扩展名
    supported_extensions = {
        '.py': 'python',
        '.cpp': 'cpp', '.cc': 'cpp', '.cxx': 'cpp', '.c++': 'cpp',
        '.hpp': 'cpp', '.h': 'cpp',
        '.java': 'java',
        '.js': 'javascript', '.ts': 'typescript'
    }
    
    try:
        for root, _, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()
                
                if file_ext in supported_extensions:
                    relative_path = os.path.relpath(file_path, folder_path)
                    code_files.append({
                        'path': file_path,
                        'relative_path': relative_path,
                        'name': file,
                        'extension': file_ext,
                        'language': supported_extensions[file_ext],
                        'size': os.path.getsize(file_path)
                    })
        
        print(f"   找到 {len(code_files)} 个代码文件")
        
        # 按语言分组
        files_by_lang = defaultdict(list)
        for file_info in code_files:
            files_by_lang[file_info['language']].append(file_info)
        
        for lang, files in files_by_lang.items():
            print(f"     {lang}: {len(files)} 个文件")
            
    except Exception as e:
        print(f"❌ 扫描失败: {e}")
        return
    
    # 2. 分析每个文件的导入和导出
    print("\n2. 分析文件导入导出关系...")
    
    dependency_graph = nx.DiGraph()
    import_relationships = []
    export_relationships = []
    
    for file_info in code_files:
        file_path = file_info['path']
        relative_path = file_info['relative_path']
        language = file_info['language']
        
        print(f"   分析: {relative_path}")
        
        try:
            # 读取文件内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        content = f.read()
                except Exception as e:
                    print(f"     ❌ 无法读取文件: {e}")
                    continue
            
            # 使用LocAgent解析器分析
            if supports_codeblocks(file_path):
                parser = get_parser_by_path(file_path)
                if parser:
                    try:
                        module = parser.parse(content, file_path=relative_path)
                        
                        # 提取导入关系
                        imports = []
                        exports = []
                        
                        def extract_relationships(block):
                            """递归提取代码块的关系"""
                            # 检查导入
                            if hasattr(block, 'type') and block.type.name == 'IMPORT':
                                if block.identifier:
                                    imports.append({
                                        'type': 'import',
                                        'target': block.identifier,
                                        'line': block.start_line
                                    })
                            
                            # 检查函数和类定义（作为导出）
                            if hasattr(block, 'type') and block.type.name in ['FUNCTION', 'CLASS']:
                                if block.identifier:
                                    exports.append({
                                        'type': block.type.name.lower(),
                                        'name': block.identifier,
                                        'line': block.start_line
                                    })
                            
                            # 检查关系
                            if hasattr(block, 'relationships'):
                                for rel in block.relationships:
                                    if hasattr(rel, 'identifier') and rel.identifier:
                                        imports.append({
                                            'type': 'reference',
                                            'target': rel.identifier,
                                            'scope': rel.scope.value if hasattr(rel, 'scope') else 'unknown',
                                            'line': block.start_line
                                        })
                            
                            # 递归处理子块
                            if hasattr(block, 'children'):
                                for child in block.children:
                                    extract_relationships(child)
                        
                        extract_relationships(module)
                        
                        file_dependencies[relative_path] = imports
                        file_exports[relative_path] = exports
                        
                        # 添加到图中
                        dependency_graph.add_node(relative_path, **file_info)
                        
                        print(f"     ✅ 导入: {len(imports)}, 导出: {len(exports)}")
                        
                    except Exception as e:
                        print(f"     ❌ 解析失败: {e}")
                else:
                    print(f"     ⚠️  无法获取解析器")
            else:
                print(f"     ⚠️  不支持的文件类型")
                
        except Exception as e:
            print(f"     ❌ 处理失败: {e}")
    
    # 3. 构建依赖关系图
    print("\n3. 构建依赖关系图...")
    
    # 分析文件间的依赖关系
    for source_file, imports in file_dependencies.items():
        for import_info in imports:
            target = import_info['target']
            
            # 尝试匹配到具体文件
            for file_info in code_files:
                file_name = os.path.splitext(file_info['name'])[0]
                relative_path = file_info['relative_path']
                
                # 简单的匹配逻辑
                if (target in file_name or 
                    target in relative_path or 
                    file_name in target):
                    
                    dependency_graph.add_edge(
                        source_file, 
                        relative_path,
                        relationship_type=import_info['type'],
                        line=import_info.get('line', 0)
                    )
                    
                    import_relationships.append({
                        'source': source_file,
                        'target': relative_path,
                        'type': import_info['type'],
                        'line': import_info.get('line', 0)
                    })
    
    print(f"   构建了 {dependency_graph.number_of_nodes()} 个节点")
    print(f"   构建了 {dependency_graph.number_of_edges()} 条依赖边")
    
    # 4. 分析依赖关系
    print("\n4. 依赖关系分析...")
    
    # 计算入度和出度
    in_degrees = dict(dependency_graph.in_degree())
    out_degrees = dict(dependency_graph.out_degree())
    
    # 找出核心文件（被依赖最多的）
    core_files = sorted(in_degrees.items(), key=lambda x: x[1], reverse=True)[:5]
    print(f"\n   核心文件（被依赖最多）:")
    for file_path, degree in core_files:
        if degree > 0:
            print(f"     {file_path}: 被 {degree} 个文件依赖")
    
    # 找出依赖最多的文件
    dependent_files = sorted(out_degrees.items(), key=lambda x: x[1], reverse=True)[:5]
    print(f"\n   依赖最多的文件:")
    for file_path, degree in dependent_files:
        if degree > 0:
            print(f"     {file_path}: 依赖 {degree} 个文件")
    
    # 检测循环依赖
    try:
        cycles = list(nx.simple_cycles(dependency_graph))
        if cycles:
            print(f"\n   ⚠️  发现 {len(cycles)} 个循环依赖:")
            for i, cycle in enumerate(cycles[:3]):  # 只显示前3个
                print(f"     循环 {i+1}: {' -> '.join(cycle)} -> {cycle[0]}")
        else:
            print(f"\n   ✅ 未发现循环依赖")
    except Exception as e:
        print(f"\n   ❌ 循环依赖检测失败: {e}")
    
    # 5. 输出详细的依赖关系
    print(f"\n5. 详细依赖关系:")
    
    for source_file in sorted(file_dependencies.keys()):
        imports = file_dependencies[source_file]
        exports = file_exports.get(source_file, [])
        
        if imports or exports:
            print(f"\n   📁 {source_file}")
            
            if exports:
                print(f"      导出 ({len(exports)}):")
                for export in exports[:3]:  # 限制显示数量
                    print(f"        - {export['type']}: {export['name']} (行 {export['line']})")
                if len(exports) > 3:
                    print(f"        ... 还有 {len(exports) - 3} 个导出")
            
            if imports:
                print(f"      导入 ({len(imports)}):")
                for imp in imports[:3]:  # 限制显示数量
                    print(f"        - {imp['type']}: {imp['target']} (行 {imp.get('line', '?')})")
                if len(imports) > 3:
                    print(f"        ... 还有 {len(imports) - 3} 个导入")
    
    # 6. 生成依赖关系报告
    print(f"\n6. 生成依赖关系报告...")

    report = {
        'summary': {
            'total_files': len(code_files),
            'files_by_language': {lang: len(files) for lang, files in files_by_lang.items()},
            'total_dependencies': len(import_relationships),
            'files_with_dependencies': len([f for f in file_dependencies.values() if f]),
            'core_files': [{'file': f, 'dependents': d} for f, d in core_files if d > 0],
            'cycles_detected': len(cycles) if 'cycles' in locals() else 0
        },
        'files': code_files,
        'dependencies': import_relationships,
        'exports': file_exports
    }

    # 保存报告
    report_file = 'x86_dependency_report.json'
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"   ✅ 报告已保存到: {report_file}")
    except Exception as e:
        print(f"   ❌ 保存报告失败: {e}")

    print(f"\n=== 分析完成 ===")
    print(f"总结:")
    print(f"  - 分析了 {len(code_files)} 个代码文件")
    print(f"  - 发现了 {len(import_relationships)} 个依赖关系")
    print(f"  - 核心文件数: {len([f for f, d in core_files if d > 0])}")
    print(f"  - 循环依赖数: {len(cycles) if 'cycles' in locals() else 0}")

    return report  # 返回报告数据供其他脚本使用

if __name__ == "__main__":
    analyze_x86_dependencies()
