#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用LocAgent工具解析x86文件夹
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any

def analyze_x86_folder():
    """使用LocAgent的解析工具分析x86文件夹"""
    
    folder_path = r"C:\Users\<USER>\Desktop\x86"

    print(f"=== 分析文件夹: {folder_path} ===\n")
    
    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"❌ 文件夹不存在: {folder_path}")
        return
    
    # 导入LocAgent的解析工具
    try:
        from repo_index.codeblocks import get_parser_by_path, supports_codeblocks
        from repo_index.codeblocks.parser.cpp import CppParser
        from repo_index.codeblocks.parser.python import PythonParser
        from repo_index.codeblocks.parser.java import JavaParser
        print("✅ LocAgent解析工具导入成功")
    except Exception as e:
        print(f"❌ 导入LocAgent工具失败: {e}")
        return
    
    # 扫描文件夹中的代码文件
    print("\n1. 扫描代码文件...")
    code_files = []
    supported_extensions = ['.py', '.cpp', '.cc', '.cxx', '.c++', '.hpp', '.h', '.java', '.js', '.ts']
    
    try:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()
                
                if file_ext in supported_extensions:
                    relative_path = os.path.relpath(file_path, folder_path)
                    code_files.append({
                        'path': file_path,
                        'relative_path': relative_path,
                        'extension': file_ext,
                        'size': os.path.getsize(file_path)
                    })
        
        print(f"   找到 {len(code_files)} 个代码文件")
        
        # 按文件类型分组
        files_by_type = {}
        for file_info in code_files:
            ext = file_info['extension']
            if ext not in files_by_type:
                files_by_type[ext] = []
            files_by_type[ext].append(file_info)
        
        print("\n   文件类型统计:")
        for ext, files in files_by_type.items():
            print(f"     {ext}: {len(files)} 个文件")
            
    except Exception as e:
        print(f"❌ 扫描文件失败: {e}")
        return
    
    # 解析代码文件
    print("\n2. 解析代码文件...")
    
    parsed_results = []
    
    for file_info in code_files[:10]:  # 限制解析前10个文件以避免输出过长
        file_path = file_info['path']
        relative_path = file_info['relative_path']
        
        print(f"\n   解析: {relative_path}")
        
        try:
            # 检查是否支持该文件类型
            if not supports_codeblocks(file_path):
                print(f"     ⚠️  不支持的文件类型")
                continue
            
            # 获取对应的解析器
            parser = get_parser_by_path(file_path)
            if not parser:
                print(f"     ❌ 无法获取解析器")
                continue
            
            print(f"     📝 使用解析器: {type(parser).__name__}")
            
            # 读取文件内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        content = f.read()
                except:
                    print(f"     ❌ 无法读取文件编码")
                    continue
            
            # 解析代码
            module = parser.parse(content, file_path=relative_path)
            
            # 收集解析结果
            result = {
                'file': relative_path,
                'parser': type(parser).__name__,
                'module_type': module.type.name,
                'children_count': len(module.children),
                'language': module.language,
                'structures': []
            }
            
            # 分析代码结构
            def analyze_structure(block, depth=0):
                if depth > 3:  # 限制深度
                    return
                
                structure_info = {
                    'type': block.type.name,
                    'identifier': block.identifier,
                    'start_line': block.start_line,
                    'end_line': block.end_line,
                    'children_count': len(block.children),
                    'depth': depth
                }
                
                result['structures'].append(structure_info)
                
                # 递归分析子结构
                for child in block.children:
                    analyze_structure(child, depth + 1)
            
            analyze_structure(module)
            parsed_results.append(result)
            
            print(f"     ✅ 解析成功 - {result['children_count']} 个子结构")
            
        except Exception as e:
            print(f"     ❌ 解析失败: {e}")
    
    # 输出解析结果摘要
    print(f"\n3. 解析结果摘要")
    print(f"   总文件数: {len(code_files)}")
    print(f"   成功解析: {len(parsed_results)}")
    
    # 按解析器类型统计
    parser_stats = {}
    for result in parsed_results:
        parser_name = result['parser']
        if parser_name not in parser_stats:
            parser_stats[parser_name] = 0
        parser_stats[parser_name] += 1
    
    print(f"\n   解析器使用统计:")
    for parser_name, count in parser_stats.items():
        print(f"     {parser_name}: {count} 个文件")
    
    # 显示详细的解析结果
    print(f"\n4. 详细解析结果:")
    for result in parsed_results:
        print(f"\n   📁 {result['file']}")
        print(f"      解析器: {result['parser']}")
        print(f"      语言: {result['language']}")
        print(f"      模块类型: {result['module_type']}")
        print(f"      结构数量: {len(result['structures'])}")
        
        # 显示主要结构
        main_structures = [s for s in result['structures'] if s['depth'] <= 1 and s['type'] in ['CLASS', 'FUNCTION', 'MODULE']]
        if main_structures:
            print(f"      主要结构:")
            for struct in main_structures[:5]:  # 限制显示前5个
                identifier = struct['identifier'] or '(无名)'
                print(f"        - {struct['type']}: {identifier} (行 {struct['start_line']}-{struct['end_line']})")
    
    print(f"\n=== 分析完成 ===")

if __name__ == "__main__":
    analyze_x86_folder()
