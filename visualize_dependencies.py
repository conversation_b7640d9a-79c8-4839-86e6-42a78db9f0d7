#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化x86文件夹的依赖关系
"""

import json
import matplotlib.pyplot as plt
import networkx as nx
from collections import defaultdict
import numpy as np

# 设置中文字体（可选，如果系统支持的话）
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
except:
    pass

def visualize_dependencies():
    """可视化依赖关系"""
    
    # 读取依赖关系报告
    try:
        with open('x86_dependency_report.json', 'r', encoding='utf-8') as f:
            report = json.load(f)
    except FileNotFoundError:
        print("❌ 找不到依赖关系报告文件")
        print("请先运行 analyze_x86_dependencies.py 生成报告")
        return
    
    print("=== x86依赖关系可视化 ===\n")
    
    # 1. 创建核心文件依赖图
    print("1. 生成核心文件依赖图...")
    
    plt.figure(figsize=(15, 10))
    
    # 子图1: 核心文件被依赖情况
    plt.subplot(2, 2, 1)
    core_files = report['summary']['core_files']
    files = [f['file'].split('\\')[-1] for f in core_files]  # 只取文件名
    dependents = [f['dependents'] for f in core_files]
    
    bars = plt.bar(range(len(files)), dependents, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])
    plt.xlabel('Core Files')
    plt.ylabel('Dependents Count')
    plt.title('Core Files Dependency Count')
    plt.xticks(range(len(files)), files, rotation=45, ha='right')
    
    # 添加数值标签
    for bar, count in zip(bars, dependents):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                str(count), ha='center', va='bottom')
    
    # 子图2: 语言分布饼图
    plt.subplot(2, 2, 2)
    languages = list(report['summary']['files_by_language'].keys())
    counts = list(report['summary']['files_by_language'].values())
    colors = ['#FF9999', '#66B2FF']
    
    plt.pie(counts, labels=languages, autopct='%1.1f%%', colors=colors, startangle=90)
    plt.title('Language Distribution')
    
    # 子图3: 依赖关系统计
    plt.subplot(2, 2, 3)
    stats = [
        report['summary']['total_files'],
        report['summary']['files_with_dependencies'],
        report['summary']['total_dependencies'],
        report['summary']['cycles_detected']
    ]
    labels = ['Total Files', 'Files w/ Deps', 'Dependencies', 'Cycles']
    colors = ['#FFB6C1', '#98FB98', '#87CEEB', '#F0E68C']
    
    bars = plt.bar(labels, stats, color=colors)
    plt.ylabel('Count')
    plt.title('Dependency Statistics')
    plt.xticks(rotation=45, ha='right')
    
    # 添加数值标签
    for bar, count in zip(bars, stats):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(stats)*0.01, 
                str(count), ha='center', va='bottom')
    
    # 子图4: 文件类型分布
    plt.subplot(2, 2, 4)
    
    # 分析文件扩展名分布
    file_types = defaultdict(int)
    for file_info in report['files']:
        ext = file_info['extension']
        file_types[ext] += 1
    
    types = list(file_types.keys())
    type_counts = list(file_types.values())
    
    plt.bar(types, type_counts, color=['#FF7F7F', '#7FFF7F', '#7F7FFF'])
    plt.xlabel('File Type')
    plt.ylabel('File Count')
    plt.title('File Type Distribution')
    
    # 添加数值标签
    for i, count in enumerate(type_counts):
        plt.text(i, count + max(type_counts)*0.01, str(count), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('x86_dependencies_overview.png', dpi=300, bbox_inches='tight')
    print("   ✅ 保存到: x86_dependencies_overview.png")
    
    # 2. 创建网络图
    print("\n2. 生成依赖网络图...")
    
    plt.figure(figsize=(20, 15))
    
    # 创建有向图
    G = nx.DiGraph()
    
    # 添加核心文件和它们的依赖关系
    dependencies = report.get('dependencies', [])
    
    # 只显示核心文件相关的依赖关系
    core_file_names = [f['file'] for f in core_files]
    
    # 筛选与核心文件相关的依赖
    core_dependencies = []
    for dep in dependencies:
        if any(core_file in dep['source'] or core_file in dep['target'] 
               for core_file in core_file_names):
            core_dependencies.append(dep)
    
    # 限制显示的依赖关系数量
    core_dependencies = core_dependencies[:100]  # 只显示前100个
    
    for dep in core_dependencies:
        source = dep['source'].split('\\')[-1]  # 只取文件名
        target = dep['target'].split('\\')[-1]
        G.add_edge(source, target)
    
    if G.number_of_nodes() > 0:
        # 使用spring布局
        pos = nx.spring_layout(G, k=3, iterations=50)
        
        # 计算节点大小（基于度数）
        node_sizes = []
        for node in G.nodes():
            degree = G.degree(node)
            node_sizes.append(max(300, degree * 100))
        
        # 绘制网络图
        nx.draw_networkx_nodes(G, pos, node_size=node_sizes, 
                              node_color='lightblue', alpha=0.7)
        nx.draw_networkx_edges(G, pos, edge_color='gray', 
                              arrows=True, arrowsize=20, alpha=0.5)
        nx.draw_networkx_labels(G, pos, font_size=8, font_weight='bold')
        
        plt.title('Core Files Dependency Network', fontsize=16, fontweight='bold')
        plt.axis('off')
        plt.savefig('x86_dependency_network.png', dpi=300, bbox_inches='tight')
        print("   ✅ 保存到: x86_dependency_network.png")
    else:
        print("   ⚠️  没有足够的依赖关系数据生成网络图")
    
    # 3. 分析模块分布
    print("\n3. 分析模块分布...")
    
    plt.figure(figsize=(15, 8))
    
    # 按文件名前缀分组
    module_groups = defaultdict(list)
    for file_info in report['files']:
        filename = file_info['name']
        # 提取模块名（文件名的第一部分）
        if '_' in filename:
            module = filename.split('_')[0]
        else:
            module = filename.split('.')[0]
        module_groups[module].append(file_info)
    
    # 只显示文件数量最多的前15个模块
    sorted_modules = sorted(module_groups.items(), key=lambda x: len(x[1]), reverse=True)[:15]
    
    modules = [item[0] for item in sorted_modules]
    module_counts = [len(item[1]) for item in sorted_modules]
    
    plt.bar(range(len(modules)), module_counts, color=plt.cm.Set3(np.linspace(0, 1, len(modules))))
    plt.xlabel('Module Name')
    plt.ylabel('File Count')
    plt.title('Main Module File Distribution')
    plt.xticks(range(len(modules)), modules, rotation=45, ha='right')
    
    # 添加数值标签
    for i, count in enumerate(module_counts):
        plt.text(i, count + max(module_counts)*0.01, str(count), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('x86_module_distribution.png', dpi=300, bbox_inches='tight')
    print("   ✅ 保存到: x86_module_distribution.png")
    
    # 4. 生成总结报告
    print("\n4. 生成可视化总结...")
    
    print(f"\n📊 可视化分析完成!")
    print(f"   - 总文件数: {report['summary']['total_files']}")
    print(f"   - 依赖关系: {report['summary']['total_dependencies']}")
    print(f"   - 核心文件: {len(core_files)}")
    print(f"   - 主要模块: {len(sorted_modules)}")
    print(f"   - 循环依赖: {report['summary']['cycles_detected']}")
    
    print(f"\n📁 生成的图表文件:")
    print(f"   - x86_dependencies_overview.png (总体概况)")
    print(f"   - x86_dependency_network.png (依赖网络图)")
    print(f"   - x86_module_distribution.png (模块分布)")
    
    # 不显示图表窗口，只保存文件
    # plt.show()

if __name__ == "__main__":
    visualize_dependencies()
