# C++解析器实现和x86依赖分析

这个项目扩展了LocAgent的功能，添加了C++语言支持，并提供了强大的代码依赖关系分析工具。

## 🚀 功能特性

### 1. C++解析器支持
- ✅ 完整的C++语法解析
- ✅ 支持类、函数、命名空间、模板等
- ✅ 识别include语句和依赖关系
- ✅ 支持多种C++文件扩展名 (.cpp, .cc, .cxx, .c++, .hpp, .h)

### 2. 依赖关系分析
- ✅ 自动扫描代码文件
- ✅ 分析导入导出关系
- ✅ 构建依赖关系图
- ✅ 检测循环依赖
- ✅ 识别核心文件

### 3. 可视化报告
- ✅ 生成详细的分析报告
- ✅ 创建依赖关系图表
- ✅ 模块分布统计
- ✅ 导出JSON格式数据

## 📁 文件结构

```
├── repo_index/codeblocks/parser/
│   ├── cpp.py                    # C++解析器实现
│   ├── queries/cpp.scm          # C++语法查询规则
│   └── create.py                # 更新的解析器工厂
├── analyze_x86_dependencies.py  # 主要的依赖分析脚本
├── visualize_dependencies.py    # 可视化生成脚本
├── test_cpp_implementation.py   # C++解析器测试脚本
└── README_cpp_parser.md         # 本文档
```

## 🛠️ 安装和设置

### 1. 安装依赖
```bash
pip install tree-sitter==0.21.3
pip install tree-sitter-languages==1.10.2
pip install networkx
pip install matplotlib
pip install llama-index
pip install rapidfuzz
pip install faiss-cpu
```

### 2. 验证C++解析器
```bash
python test_cpp_implementation.py
```

## 📊 使用方法

### 1. 分析代码依赖关系
```bash
python analyze_x86_dependencies.py
```

这个脚本会：
- 扫描指定文件夹中的所有代码文件
- 使用LocAgent解析器分析每个文件
- 提取导入导出关系
- 构建依赖关系图
- 检测循环依赖
- 生成详细的JSON报告

### 2. 生成可视化图表
```bash
python visualize_dependencies.py
```

这个脚本会生成：
- `x86_dependencies_overview.png` - 总体概况图表
- `x86_dependency_network.png` - 依赖网络图
- `x86_module_distribution.png` - 模块分布图

### 3. 自定义分析目标

修改 `analyze_x86_dependencies.py` 中的路径：
```python
folder_path = r"你的目标文件夹路径"
```

## 🔧 C++解析器技术细节

### 支持的C++语法结构
- **类和结构体**: `class`, `struct`
- **函数**: 函数定义和声明
- **构造函数**: 自动识别构造函数
- **命名空间**: `namespace`
- **包含语句**: `#include`
- **控制结构**: `if`, `for`, `while`, `switch`, `try/catch`
- **变量声明**: 局部和成员变量

### Tree-sitter查询规则
C++解析器使用 `cpp.scm` 文件定义的查询规则来识别代码结构：

```scheme
; 类定义
(class_specifier
  (type_identifier) @identifier
  (field_declaration_list
    ("{") @child.first
  )
) @root @definition.class

; 函数定义
(function_definition
  (function_declarator
    (identifier) @identifier
  )
  (compound_statement
    ("{") @child.first
  )
) @root @definition.function
```

## 📈 分析结果示例

### 核心文件识别
```
核心文件（被依赖最多）:
  x86_usability.h: 被 47 个文件依赖
  x86_activation.h: 被 20 个文件依赖
  elu_riscv.cpp: 被 6 个文件依赖
```

### 依赖关系统计
```
总结:
  - 分析了 327 个代码文件
  - 发现了 2,466 个依赖关系
  - 核心文件数: 5
  - 循环依赖数: 106
```

## 🎯 应用场景

### 1. 代码库维护
- 识别关键文件和模块
- 理解代码架构
- 重构规划

### 2. 依赖管理
- 检测循环依赖
- 优化编译顺序
- 减少耦合度

### 3. 代码审查
- 分析模块职责
- 评估代码质量
- 识别潜在问题

## 🔍 高级功能

### 1. 自定义文件过滤
```python
# 在analyze_x86_dependencies.py中修改
supported_extensions = {
    '.py': 'python',
    '.cpp': 'cpp', 
    '.h': 'cpp',
    # 添加更多扩展名
}
```

### 2. 依赖关系匹配算法
脚本使用智能匹配算法来识别文件间的依赖关系：
- 文件名匹配
- 路径匹配
- 内容分析

### 3. 可扩展的解析器架构
新的语言解析器可以轻松添加到系统中：
1. 实现解析器类
2. 创建语法查询文件
3. 更新工厂方法
4. 添加文件类型检测

## 🐛 故障排除

### 常见问题

1. **编码错误**
   - 脚本会自动尝试UTF-8和GBK编码
   - 如果仍有问题，检查文件编码

2. **依赖缺失**
   - 确保安装了所有必需的Python包
   - 检查tree-sitter-languages版本兼容性

3. **内存使用**
   - 大型代码库可能需要大量内存
   - 考虑分批处理或增加系统内存

### 调试模式
在脚本中添加详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🤝 贡献

欢迎贡献代码和改进建议！

### 开发指南
1. Fork项目
2. 创建功能分支
3. 添加测试
4. 提交Pull Request

### 扩展建议
- 添加更多编程语言支持
- 改进依赖匹配算法
- 增强可视化功能
- 优化性能

## 📄 许可证

本项目遵循原LocAgent项目的许可证条款。

---

**这个工具展示了LocAgent在代码分析和依赖管理方面的强大能力，为大型代码库的维护和理解提供了宝贵的支持。**
