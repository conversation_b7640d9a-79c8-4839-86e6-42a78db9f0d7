#!/usr/bin/env python3
"""
LocAgent索引构建和检索演示
展示如何使用LocAgent的三种索引技术
"""

import os
import json
import pickle
from pathlib import Path

def demo_vector_indexing():
    """演示向量索引的构建和使用"""
    print("=== 向量索引演示 ===")
    
    try:
        from repo_index.index import CodeIndex
        from repo_index.repository import FileRepository
        from repo_index.index.settings import IndexSettings
        
        # 1. 设置索引参数
        settings = IndexSettings(
            embed_model='text-embedding-3-small',
            chunk_size=1500,
            min_chunk_size=256,
            max_chunk_size=2000,
            hard_token_limit=6000
        )
        
        # 2. 创建文件仓库
        repo_path = "path/to/your/repo"
        file_repo = FileRepository(repo_path)
        
        # 3. 构建向量索引
        print("构建向量索引...")
        code_index = CodeIndex(
            file_repo=file_repo,
            settings=settings
        )
        
        # 4. 索引代码库
        code_index.build_index(
            repo_path=repo_path,
            num_workers=4,
            show_progress=True
        )
        
        # 5. 保存索引
        persist_dir = "index_data/vector_index"
        code_index.persist(persist_dir)
        print(f"向量索引已保存到: {persist_dir}")
        
        # 6. 语义搜索示例
        print("\n语义搜索示例:")
        results = code_index.vector_search(
            query="implement user authentication",
            file_pattern="**/auth/**",
            category="implementation",
            max_results=5
        )
        
        for hit in results.hits:
            print(f"  文件: {hit.file_path}")
            print(f"  相似度: {hit.score:.3f}")
            print(f"  代码片段: {hit.snippet[:100]}...")
            print()
            
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保安装了所需的依赖包")

def demo_dependency_graph():
    """演示依赖图的构建和使用"""
    print("=== 依赖图索引演示 ===")
    
    try:
        from dependency_graph.build_graph import build_graph
        from dependency_graph.traverse_graph import RepoEntitySearcher, RepoDependencySearcher
        
        # 1. 构建依赖图
        repo_path = "path/to/your/repo"
        print("构建依赖关系图...")
        
        graph = build_graph(
            repo_path=repo_path,
            fuzzy_search=True,
            global_import=True
        )
        
        print(f"图统计: {graph.number_of_nodes()} 节点, {graph.number_of_edges()} 边")
        
        # 2. 保存图
        graph_file = "index_data/dependency_graph.pkl"
        with open(graph_file, 'wb') as f:
            pickle.dump(graph, f)
        print(f"依赖图已保存到: {graph_file}")
        
        # 3. 创建搜索器
        entity_searcher = RepoEntitySearcher(graph)
        dependency_searcher = RepoDependencySearcher(graph)
        
        # 4. 实体搜索示例
        print("\n实体搜索示例:")
        entities = entity_searcher.search_entities(
            query="AuthManager",
            entity_type="class",
            max_results=5
        )
        
        for entity in entities:
            print(f"  类名: {entity['name']}")
            print(f"  文件: {entity['file_path']}")
            print(f"  行号: {entity['start_line']}-{entity['end_line']}")
            print()
        
        # 5. 依赖关系查询示例
        print("依赖关系查询示例:")
        if entities:
            node_id = entities[0]['node_id']
            dependencies = dependency_searcher.find_dependencies(
                node_id=node_id,
                direction="outgoing",  # 或 "incoming"
                max_depth=2
            )
            
            for dep in dependencies[:5]:
                print(f"  依赖: {dep['target']}")
                print(f"  类型: {dep['edge_type']}")
                print(f"  深度: {dep['depth']}")
                print()
                
    except ImportError as e:
        print(f"导入错误: {e}")

def demo_bm25_retrieval():
    """演示BM25检索的构建和使用"""
    print("=== BM25检索演示 ===")
    
    try:
        from plugins.location_tools.retriever.bm25_retriever import (
            build_code_retriever_from_repo,
            build_module_retriever
        )
        from dependency_graph.traverse_graph import RepoEntitySearcher
        
        # 1. 构建BM25检索器
        repo_path = "path/to/your/repo"
        print("构建BM25检索器...")
        
        retriever = build_code_retriever_from_repo(
            repo_path=repo_path,
            similarity_top_k=10,
            chunk_size=500,
            max_chunk_size=2000,
            persist_path="index_data/bm25_index.pkl"
        )
        
        print("BM25检索器构建完成")
        
        # 2. 文本检索示例
        print("\n文本检索示例:")
        query = "user authentication login"
        results = retriever.retrieve(query)
        
        for result in results[:5]:
            print(f"  相关度: {result.score:.3f}")
            print(f"  内容: {result.text[:100]}...")
            print(f"  元数据: {result.metadata}")
            print()
        
        # 3. 模块级检索示例
        print("模块级检索示例:")
        # 假设已有依赖图
        graph_file = "index_data/dependency_graph.pkl"
        if os.path.exists(graph_file):
            with open(graph_file, 'rb') as f:
                graph = pickle.load(f)
            
            entity_searcher = RepoEntitySearcher(graph)
            module_retriever = build_module_retriever(
                entity_searcher=entity_searcher,
                search_scope="function",
                similarity_top_k=5
            )
            
            module_results = module_retriever.retrieve("authentication")
            for result in module_results[:3]:
                print(f"  模块: {result.text}")
                print(f"  相关度: {result.score:.3f}")
                print()
                
    except ImportError as e:
        print(f"导入错误: {e}")

def demo_integrated_search():
    """演示集成搜索 - 结合三种索引技术"""
    print("=== 集成搜索演示 ===")
    
    query = "implement user authentication"
    print(f"查询: {query}")
    
    results = {
        'vector_results': [],
        'graph_results': [],
        'bm25_results': []
    }
    
    # 1. 向量搜索
    try:
        # 这里应该调用实际的向量搜索
        print("执行向量搜索...")
        # results['vector_results'] = vector_search(query)
        results['vector_results'] = [
            {"file": "auth/manager.py", "score": 0.85, "type": "semantic"},
            {"file": "auth/views.py", "score": 0.78, "type": "semantic"}
        ]
    except Exception as e:
        print(f"向量搜索失败: {e}")
    
    # 2. 图搜索
    try:
        print("执行图搜索...")
        # results['graph_results'] = graph_search(query)
        results['graph_results'] = [
            {"file": "auth/models.py", "relation": "imports", "type": "structural"},
            {"file": "auth/utils.py", "relation": "invokes", "type": "structural"}
        ]
    except Exception as e:
        print(f"图搜索失败: {e}")
    
    # 3. BM25搜索
    try:
        print("执行BM25搜索...")
        # results['bm25_results'] = bm25_search(query)
        results['bm25_results'] = [
            {"file": "auth/forms.py", "score": 2.34, "type": "keyword"},
            {"file": "auth/tests.py", "score": 1.89, "type": "keyword"}
        ]
    except Exception as e:
        print(f"BM25搜索失败: {e}")
    
    # 4. 结果融合
    print("\n融合搜索结果:")
    all_files = set()
    for result_type, result_list in results.items():
        for result in result_list:
            all_files.add(result['file'])
    
    for file_path in sorted(all_files):
        print(f"  📁 {file_path}")
        for result_type, result_list in results.items():
            for result in result_list:
                if result['file'] == file_path:
                    if 'score' in result:
                        print(f"    - {result['type']}: {result['score']:.3f}")
                    else:
                        print(f"    - {result['type']}: {result.get('relation', 'N/A')}")
        print()

def demo_performance_analysis():
    """演示性能分析"""
    print("=== 性能分析演示 ===")
    
    import time
    
    # 模拟不同检索方法的性能
    methods = {
        "向量检索": {"latency": 0.12, "accuracy": 0.85, "recall": 0.78},
        "图遍历": {"latency": 0.02, "accuracy": 0.92, "recall": 0.65},
        "BM25检索": {"latency": 0.01, "accuracy": 0.75, "recall": 0.82},
        "融合检索": {"latency": 0.15, "accuracy": 0.91, "recall": 0.88}
    }
    
    print("检索方法性能对比:")
    print(f"{'方法':<10} {'延迟(s)':<10} {'准确率':<10} {'召回率':<10}")
    print("-" * 45)
    
    for method, metrics in methods.items():
        print(f"{method:<10} {metrics['latency']:<10.3f} {metrics['accuracy']:<10.3f} {metrics['recall']:<10.3f}")
    
    print("\n索引大小估算:")
    repo_stats = {
        "文件数": 1250,
        "代码行数": 125000,
        "函数数": 3500,
        "类数": 850
    }
    
    index_sizes = {
        "向量索引": "~500MB (1536维 × 8000块)",
        "依赖图": "~50MB (NetworkX图)",
        "BM25索引": "~20MB (倒排索引)",
        "总计": "~570MB"
    }
    
    for stat, value in repo_stats.items():
        print(f"  {stat}: {value}")
    
    print("\n索引大小:")
    for index_type, size in index_sizes.items():
        print(f"  {index_type}: {size}")

if __name__ == "__main__":
    print("🚀 LocAgent索引系统演示\n")
    
    # 运行各个演示
    demo_vector_indexing()
    print("\n" + "="*50 + "\n")
    
    demo_dependency_graph()
    print("\n" + "="*50 + "\n")
    
    demo_bm25_retrieval()
    print("\n" + "="*50 + "\n")
    
    demo_integrated_search()
    print("\n" + "="*50 + "\n")
    
    demo_performance_analysis()
    
    print("\n✅ 演示完成!")
    print("\n💡 提示:")
    print("  - 修改 repo_path 为你的实际代码库路径")
    print("  - 确保安装了所需的依赖包")
    print("  - 根据需要调整索引参数")
