aiohappyeyeballs==2.4.3
aiohttp==3.10.10
aiosignal==1.3.1
annotated-types==0.7.0
anyio==4.6.2.post1
asttokens==2.4.1
attrs==24.2.0
azure-core==1.32.0
azure-identity==1.19.0
beautifulsoup4==4.12.3
bm25s==0.2.3
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.4.0
click==8.1.7
comm==0.2.2
contourpy==1.3.0
cryptography==43.0.3
cycler==0.12.1
dataclasses-json==0.6.7
datasets==3.1.0
debugpy==1.8.8
decorator==5.1.1
Deprecated==1.2.14
dill==0.3.8
dirtyjson==1.0.8
distro==1.9.0
exceptiongroup==1.2.2
executing==2.1.0
faiss-cpu==1.8.0
filelock==3.16.1
fonttools==4.54.1
frozenlist==1.5.0
fsspec==2024.9.0
greenlet==3.1.1
h11==0.14.0
httpcore==1.0.6
httpx==0.27.2
huggingface-hub==0.26.2
idna==3.10
importlib_metadata==8.5.0
ipykernel==6.29.5
ipython==8.29.0
jedi==0.19.1
Jinja2==3.1.4
jiter==0.7.0
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.3
jupyter_core==5.7.2
kiwisolver==1.4.7
libcst==1.5.0
litellm==1.52.1
llama-cloud==0.1.4
llama-index==0.11.22
llama-index-agent-openai==0.3.4
llama-index-cli==0.3.1
llama-index-core==0.11.22
llama-index-embeddings-azure-openai==0.2.5
llama-index-embeddings-openai==0.2.5
llama-index-indices-managed-llama-cloud==0.4.0
llama-index-legacy==0.9.48.post4
llama-index-llms-azure-openai==0.2.2
llama-index-llms-openai==0.2.16
llama-index-multi-modal-llms-openai==0.2.3
llama-index-program-openai==0.2.0
llama-index-question-gen-openai==0.2.0
llama-index-readers-file==0.2.2
llama-index-readers-llama-parse==0.3.0
llama-index-retrievers-bm25==0.4.0
llama-parse==0.5.13
MarkupSafe==3.0.2
marshmallow==3.23.1
matplotlib==3.9.2
matplotlib-inline==0.1.7
mpmath==1.3.0
msal==1.31.0
msal-extensions==1.2.0
multidict==6.1.0
multiprocess==0.70.16
mypy-extensions==1.0.0
nest_asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
openai==1.54.3
packaging==24.2
pandas==2.2.3
parso==0.8.4
pexpect==4.9.0
pickleshare==0.7.5
pillow==11.0.0
pip==24.3.1
platformdirs==4.3.6
portalocker==2.10.1
prompt_toolkit==3.0.48
propcache==0.2.0
psutil==6.1.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==18.0.0
pycparser==2.22
pydantic==2.9.2
pydantic_core==2.23.4
Pygments==2.18.0
PyJWT==2.9.0
pyparsing==3.2.0
pypdf==4.3.1
PyStemmer==*******
python-dateutil==2.9.0
python-dotenv==1.0.1
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.2.0
RapidFuzz==3.10.1
referencing==0.35.1
regex==2024.11.6
requests==2.32.3
rpds-py==0.21.0
scipy==1.14.1
setuptools==75.3.0
six==1.16.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.36
stack-data==0.6.2
striprtf==0.0.26
sympy==1.13.1
tenacity==8.5.0
tiktoken==0.8.0
tokenizers==0.20.3
toml==0.10.2
torch==2.5.1
tornado==6.4.1
tqdm==4.67.0
traitlets==5.14.3
tree-sitter==0.21.3
tree-sitter-languages==1.10.2
triton==3.1.0
typing_extensions==4.12.2
typing-inspect==0.9.0
tzdata==2024.2
unidiff==0.7.5
urllib3==2.2.3
wcwidth==0.2.13
wheel==0.44.0
wrapt==1.16.0
xxhash==3.5.0
yarl==1.17.1
zipp==3.20.2
